"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/DashboardOverview */ \"(app-pages-browser)/./src/components/dashboard/DashboardOverview.tsx\");\n/* harmony import */ var _components_dashboard_PurchaseMiningUnit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/PurchaseMiningUnit */ \"(app-pages-browser)/./src/components/dashboard/PurchaseMiningUnit.tsx\");\n/* harmony import */ var _components_dashboard_EarningsTracker__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/EarningsTracker */ \"(app-pages-browser)/./src/components/dashboard/EarningsTracker.tsx\");\n/* harmony import */ var _components_dashboard_WalletDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/WalletDashboard */ \"(app-pages-browser)/./src/components/dashboard/WalletDashboard.tsx\");\n/* harmony import */ var _components_dashboard_D3BinaryTree__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/D3BinaryTree */ \"(app-pages-browser)/./src/components/dashboard/D3BinaryTree.tsx\");\n/* harmony import */ var _components_dashboard_KYCPortal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/KYCPortal */ \"(app-pages-browser)/./src/components/dashboard/KYCPortal.tsx\");\n/* harmony import */ var _components_dashboard_MiningUnitsTable__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/MiningUnitsTable */ \"(app-pages-browser)/./src/components/dashboard/MiningUnitsTable.tsx\");\n/* harmony import */ var _components_dashboard_SupportCenter__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/SupportCenter */ \"(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx\");\n/* harmony import */ var _components_dashboard_UserProfileSettings__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dashboard/UserProfileSettings */ \"(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.Loading, {\n                size: \"lg\",\n                text: \"Loading dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'overview':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_5__.DashboardOverview, {\n                    onTabChange: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, this);\n            case 'mining':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_PurchaseMiningUnit__WEBPACK_IMPORTED_MODULE_6__.PurchaseMiningUnit, {\n                            onPurchaseComplete: ()=>window.location.reload()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MiningUnitsTable__WEBPACK_IMPORTED_MODULE_11__.MiningUnitsTable, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this);\n            case 'earnings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_EarningsTracker__WEBPACK_IMPORTED_MODULE_7__.EarningsTracker, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 16\n                }, this);\n            case 'wallet':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_WalletDashboard__WEBPACK_IMPORTED_MODULE_8__.WalletDashboard, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            case 'referrals':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_D3BinaryTree__WEBPACK_IMPORTED_MODULE_9__.D3BinaryTree, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n            case 'kyc':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_KYCPortal__WEBPACK_IMPORTED_MODULE_10__.KYCPortal, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, this);\n            case 'support':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SupportCenter__WEBPACK_IMPORTED_MODULE_12__.SupportCenter, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, this);\n            case 'profile':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_UserProfileSettings__WEBPACK_IMPORTED_MODULE_13__.UserProfileSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_5__.DashboardOverview, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardLayout, {\n        activeTab: activeTab,\n        onTabChange: setActiveTab,\n        children: renderTabContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"0qbidGbtml+xVXtO5xgDD95S49I=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});