"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardOverview */ \"(app-pages-browser)/./src/components/dashboard/DashboardOverview.tsx\");\n/* harmony import */ var _components_dashboard_PurchaseMiningUnit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/PurchaseMiningUnit */ \"(app-pages-browser)/./src/components/dashboard/PurchaseMiningUnit.tsx\");\n/* harmony import */ var _components_dashboard_EarningsTracker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/EarningsTracker */ \"(app-pages-browser)/./src/components/dashboard/EarningsTracker.tsx\");\n/* harmony import */ var _components_dashboard_WalletDashboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/WalletDashboard */ \"(app-pages-browser)/./src/components/dashboard/WalletDashboard.tsx\");\n/* harmony import */ var _components_dashboard_D3BinaryTree__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/D3BinaryTree */ \"(app-pages-browser)/./src/components/dashboard/D3BinaryTree.tsx\");\n/* harmony import */ var _components_dashboard_KYCPortal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/KYCPortal */ \"(app-pages-browser)/./src/components/dashboard/KYCPortal.tsx\");\n/* harmony import */ var _components_dashboard_MiningUnitsTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/MiningUnitsTable */ \"(app-pages-browser)/./src/components/dashboard/MiningUnitsTable.tsx\");\n/* harmony import */ var _components_dashboard_SupportCenter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/SupportCenter */ \"(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx\");\n/* harmony import */ var _components_dashboard_UserProfileSettings__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/UserProfileSettings */ \"(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx\");\n/* harmony import */ var _components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/auth/AuthGuard */ \"(app-pages-browser)/./src/components/auth/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardContent() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'overview':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_3__.DashboardOverview, {\n                    onTabChange: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 16\n                }, this);\n            case 'mining':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_PurchaseMiningUnit__WEBPACK_IMPORTED_MODULE_4__.PurchaseMiningUnit, {\n                            onPurchaseComplete: ()=>window.location.reload()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MiningUnitsTable__WEBPACK_IMPORTED_MODULE_9__.MiningUnitsTable, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this);\n            case 'earnings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_EarningsTracker__WEBPACK_IMPORTED_MODULE_5__.EarningsTracker, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, this);\n            case 'wallet':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_WalletDashboard__WEBPACK_IMPORTED_MODULE_6__.WalletDashboard, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 16\n                }, this);\n            case 'referrals':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_D3BinaryTree__WEBPACK_IMPORTED_MODULE_7__.D3BinaryTree, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, this);\n            case 'kyc':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_KYCPortal__WEBPACK_IMPORTED_MODULE_8__.KYCPortal, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 16\n                }, this);\n            case 'support':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SupportCenter__WEBPACK_IMPORTED_MODULE_10__.SupportCenter, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case 'profile':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_UserProfileSettings__WEBPACK_IMPORTED_MODULE_11__.UserProfileSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_3__.DashboardOverview, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__.DashboardLayout, {\n        activeTab: activeTab,\n        onTabChange: setActiveTab,\n        children: renderTabContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardContent, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = DashboardContent;\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_12__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_c1 = DashboardPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"DashboardContent\");\n$RefreshReg$(_c1, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/AuthGuard.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/AuthGuard.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   GuestRoute: () => (/* binding */ GuestRoute),\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute),\n/* harmony export */   withAuthGuard: () => (/* binding */ withAuthGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,withAuthGuard,ProtectedRoute,GuestRoute auto */ var _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst AuthGuard = (param)=>{\n    let { children, requireAuth = false, requireGuest = false, redirectTo } = param;\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            if (loading) return; // Wait for auth check to complete\n            // If authentication is required but user is not logged in\n            if (requireAuth && !user) {\n                const loginUrl = \"/login?redirect=\".concat(encodeURIComponent(pathname));\n                router.replace(loginUrl);\n                return;\n            }\n            // If guest access is required but user is logged in\n            if (requireGuest && user) {\n                const destination = redirectTo || '/dashboard';\n                router.replace(destination);\n                return;\n            }\n        }\n    }[\"AuthGuard.useEffect\"], [\n        user,\n        loading,\n        requireAuth,\n        requireGuest,\n        router,\n        pathname,\n        redirectTo\n    ]);\n    // Show loading while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Loading, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Checking authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render children if auth requirements are not met\n    if (requireAuth && !user) {\n        return null; // Will redirect in useEffect\n    }\n    if (requireGuest && user) {\n        return null; // Will redirect in useEffect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_s(AuthGuard, \"+V/1yikrC2yNn6BpNR6HilodE6g=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AuthGuard;\n// Higher-order component for protecting pages\nconst withAuthGuard = function(Component) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n            lineNumber: 73,\n            columnNumber: 5\n        }, _this);\n    WrappedComponent.displayName = \"withAuthGuard(\".concat(Component.displayName || Component.name, \")\");\n    return WrappedComponent;\n};\n// Convenience components\nconst ProtectedRoute = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requireAuth: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = ProtectedRoute;\nconst GuestRoute = (param)=>{\n    let { children, redirectTo } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requireGuest: true,\n        redirectTo: redirectTo,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n        lineNumber: 92,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = GuestRoute;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AuthGuard\");\n$RefreshReg$(_c1, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"GuestRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/AuthGuard.tsx\n"));

/***/ })

});