import { NextRequest, NextResponse } from 'next/server';
import { generateToken, verifyToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Test JWT generation and verification
    const testPayload = { userId: 'test-123', email: '<EMAIL>' };
    
    console.log('Testing JWT system...');
    console.log('Test payload:', testPayload);
    
    // Generate token
    const token = generateToken(testPayload);
    console.log('Generated token:', token);
    
    // Verify token
    const decoded = verifyToken(token);
    console.log('Decoded token:', decoded);
    
    // Check if verification worked
    const isValid = decoded && decoded.userId === testPayload.userId && decoded.email === testPayload.email;
    
    // Test cookie reading
    const cookieToken = request.cookies.get('auth-token')?.value;
    console.log('Cookie token exists:', !!cookieToken);
    
    let cookieDecoded = null;
    if (cookieToken) {
      cookieDecoded = verifyToken(cookieToken);
      console.log('Cookie token decoded:', cookieDecoded);
    }
    
    return NextResponse.json({
      success: true,
      data: {
        jwtTest: {
          generated: !!token,
          verified: !!decoded,
          valid: isValid,
          payload: testPayload,
          decoded: decoded,
        },
        cookieTest: {
          exists: !!cookieToken,
          decoded: cookieDecoded,
        },
      },
    });
    
  } catch (error: any) {
    console.error('Auth test error:', error);
    
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}
