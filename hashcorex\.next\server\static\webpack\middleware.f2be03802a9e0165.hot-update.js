"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(middleware)/./src/lib/auth.ts\");\n\n\n// Define protected and auth routes\nconst protectedRoutes = [\n    '/dashboard',\n    '/admin'\n];\nconst authRoutes = [\n    '/login',\n    '/register'\n];\nconst publicRoutes = [\n    '/',\n    '/about',\n    '/contact'\n];\nfunction middleware(request) {\n    const { pathname } = request.nextUrl;\n    console.log('Middleware - pathname:', pathname);\n    // Get token from cookie\n    const token = request.cookies.get('auth-token')?.value;\n    console.log('Middleware - token exists:', !!token);\n    // Verify token\n    let isAuthenticated = false;\n    let user = null;\n    if (token) {\n        try {\n            const decoded = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyToken)(token);\n            console.log('Middleware - token decoded:', !!decoded);\n            if (decoded) {\n                isAuthenticated = true;\n                user = decoded;\n            }\n        } catch (error) {\n            console.log('Middleware - token verification failed:', error);\n            // Token is invalid, remove it\n            const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            response.cookies.delete('auth-token');\n            return response;\n        }\n    }\n    console.log('Middleware - isAuthenticated:', isAuthenticated);\n    // Check if the current path is a protected route\n    const isProtectedRoute = protectedRoutes.some((route)=>pathname.startsWith(route));\n    // Check if the current path is an auth route (login/register)\n    const isAuthRoute = authRoutes.some((route)=>pathname.startsWith(route));\n    // If user is not authenticated and trying to access protected route\n    if (!isAuthenticated && isProtectedRoute) {\n        const loginUrl = new URL('/login', request.url);\n        loginUrl.searchParams.set('redirect', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(loginUrl);\n    }\n    // If user is authenticated and trying to access auth routes\n    if (isAuthenticated && isAuthRoute) {\n        // Check if there's a redirect parameter\n        const redirectUrl = request.nextUrl.searchParams.get('redirect');\n        if (redirectUrl && redirectUrl.startsWith('/')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(redirectUrl, request.url));\n        }\n        // Default redirect to dashboard\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n    }\n    // For all other routes, continue normally\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\n// Configure which routes the middleware should run on\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     * - dashboard (temporarily disabled for debugging)\n     */ '/((?!api|_next/static|_next/image|favicon.ico|uploads|crypto-icons|dashboard).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});