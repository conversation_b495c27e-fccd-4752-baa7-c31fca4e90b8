/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wallet/deposit/verify/route";
exports.ids = ["app/api/wallet/deposit/verify/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute&page=%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute&page=%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_wallet_deposit_verify_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wallet/deposit/verify/route.ts */ \"(rsc)/./src/app/api/wallet/deposit/verify/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wallet/deposit/verify/route\",\n        pathname: \"/api/wallet/deposit/verify\",\n        filename: \"route\",\n        bundlePath: \"app/api/wallet/deposit/verify/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\wallet\\\\deposit\\\\verify\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_wallet_deposit_verify_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ3YWxsZXQlMkZkZXBvc2l0JTJGdmVyaWZ5JTJGcm91dGUmcGFnZT0lMkZhcGklMkZ3YWxsZXQlMkZkZXBvc2l0JTJGdmVyaWZ5JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGd2FsbGV0JTJGZGVwb3NpdCUyRnZlcmlmeSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNkcmVhbSU1Q0Rlc2t0b3AlNUNIYXNoX01pbmluZ3MlNUNoYXNoY29yZXglNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2RyZWFtJTVDRGVza3RvcCU1Q0hhc2hfTWluaW5ncyU1Q2hhc2hjb3JleCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDb0Q7QUFDakk7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFx3YWxsZXRcXFxcZGVwb3NpdFxcXFx2ZXJpZnlcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3dhbGxldC9kZXBvc2l0L3ZlcmlmeS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3dhbGxldC9kZXBvc2l0L3ZlcmlmeVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvd2FsbGV0L2RlcG9zaXQvdmVyaWZ5L3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcZHJlYW1cXFxcRGVza3RvcFxcXFxIYXNoX01pbmluZ3NcXFxcaGFzaGNvcmV4XFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHdhbGxldFxcXFxkZXBvc2l0XFxcXHZlcmlmeVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute&page=%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wallet/deposit/verify/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/wallet/deposit/verify/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_trongrid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/trongrid */ \"(rsc)/./src/lib/trongrid.ts\");\n/* harmony import */ var _lib_depositVerificationService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/depositVerificationService */ \"(rsc)/./src/lib/depositVerificationService.ts\");\n\n\n\n\n\n// Rate limiting map to prevent abuse\nconst rateLimitMap = new Map();\nconst RATE_LIMIT_WINDOW = 60000; // 1 minute\nconst RATE_LIMIT_MAX_REQUESTS = 5; // 5 requests per minute per user\nfunction checkRateLimit(userId) {\n    const now = Date.now();\n    const userLimit = rateLimitMap.get(userId);\n    if (!userLimit || now > userLimit.resetTime) {\n        rateLimitMap.set(userId, {\n            count: 1,\n            resetTime: now + RATE_LIMIT_WINDOW\n        });\n        return true;\n    }\n    if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {\n        return false;\n    }\n    userLimit.count++;\n    return true;\n}\n// POST - Verify deposit transaction\nasync function POST(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        // Check rate limiting\n        if (!checkRateLimit(user.id)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Too many verification requests. Please wait before trying again.'\n            }, {\n                status: 429\n            });\n        }\n        const body = await request.json();\n        const { transactionId } = body;\n        // Validation\n        if (!transactionId || typeof transactionId !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Transaction ID is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!(0,_lib_trongrid__WEBPACK_IMPORTED_MODULE_3__.isValidTronTransactionId)(transactionId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid Tron transaction ID format'\n            }, {\n                status: 400\n            });\n        }\n        // Check if transaction already exists\n        const existingDeposit = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.depositTransactionDb.findByTransactionId(transactionId);\n        if (existingDeposit) {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_2__.systemLogDb.create({\n                action: 'DEPOSIT_DUPLICATE_ATTEMPT',\n                userId: user.id,\n                details: {\n                    transactionId,\n                    existingDepositId: existingDeposit.id,\n                    existingStatus: existingDeposit.status\n                },\n                ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n                userAgent: request.headers.get('user-agent') || 'unknown'\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'This transaction ID has already been submitted. Please check your deposit history.',\n                data: {\n                    existingStatus: existingDeposit.status,\n                    submittedAt: existingDeposit.createdAt\n                }\n            }, {\n                status: 400\n            });\n        }\n        // Get deposit settings - try both camelCase and UPPER_CASE keys for compatibility\n        let depositAddress = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('usdtDepositAddress');\n        if (!depositAddress) {\n            depositAddress = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');\n        }\n        // Clean deposit address - remove quotes and extra characters\n        if (depositAddress) {\n            depositAddress = depositAddress.replace(/['\"]/g, '').trim();\n        }\n        let minDepositAmount = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('minDepositAmount');\n        if (!minDepositAmount) {\n            minDepositAmount = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('MIN_DEPOSIT_AMOUNT');\n        }\n        minDepositAmount = parseFloat(minDepositAmount || '10');\n        let maxDepositAmount = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('maxDepositAmount');\n        if (!maxDepositAmount) {\n            maxDepositAmount = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('MAX_DEPOSIT_AMOUNT');\n        }\n        maxDepositAmount = parseFloat(maxDepositAmount || '10000');\n        let depositEnabled = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('depositEnabled');\n        if (!depositEnabled) {\n            depositEnabled = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('DEPOSIT_ENABLED');\n        }\n        depositEnabled = depositEnabled === 'true' || depositEnabled === true;\n        let minConfirmations = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('minConfirmations');\n        if (!minConfirmations) {\n            minConfirmations = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('MIN_CONFIRMATIONS');\n        }\n        minConfirmations = parseInt(minConfirmations || '1');\n        if (!depositEnabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Deposits are currently disabled'\n            }, {\n                status: 503\n            });\n        }\n        if (!depositAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Deposit address not configured. Please contact support.'\n            }, {\n                status: 503\n            });\n        }\n        // Log verification attempt\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.systemLogDb.create({\n            action: 'DEPOSIT_VERIFICATION_ATTEMPT',\n            userId: user.id,\n            details: {\n                transactionId\n            },\n            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n            userAgent: request.headers.get('user-agent') || 'unknown'\n        });\n        // Immediately create deposit record with PENDING_VERIFICATION status\n        // This happens regardless of transaction validity\n        const depositRecord = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.depositTransactionDb.create({\n            userId: user.id,\n            transactionId,\n            amount: 0,\n            usdtAmount: 0,\n            tronAddress: depositAddress,\n            senderAddress: '',\n            blockNumber: '',\n            blockTimestamp: new Date(),\n            confirmations: 0\n        });\n        // Update status to PENDING_VERIFICATION\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.depositTransactionDb.updateStatus(transactionId, 'PENDING_VERIFICATION');\n        // Create a pending transaction record for this deposit\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.transactionDb.create({\n            userId: user.id,\n            type: 'DEPOSIT',\n            amount: 0,\n            description: `USDT TRC20 Deposit - TX: ${transactionId}`,\n            status: 'PENDING'\n        });\n        // Start background verification process with the current admin-configured deposit address\n        await _lib_depositVerificationService__WEBPACK_IMPORTED_MODULE_4__.depositVerificationService.addTransactionForVerification(transactionId, depositAddress);\n        // Log deposit submission\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.systemLogDb.create({\n            action: 'DEPOSIT_SUBMITTED',\n            userId: user.id,\n            details: {\n                transactionId,\n                depositAddress,\n                status: 'PENDING_VERIFICATION'\n            },\n            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n            userAgent: request.headers.get('user-agent') || 'unknown'\n        });\n        // Return immediate success response\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Deposit transaction submitted successfully. We are now verifying your transaction. This may take up to 2 minutes.',\n            data: {\n                transactionId,\n                status: 'PENDING_VERIFICATION',\n                estimatedVerificationTime: 'Within 2 minutes',\n                nextSteps: [\n                    'Transaction verification in progress',\n                    'Confirmation checking will begin once transaction is found',\n                    `Wallet will be credited automatically after ${minConfirmations} confirmations`\n                ]\n            }\n        });\n    } catch (error) {\n        console.error('Deposit verification error:', error);\n        // Log error for debugging\n        if (request.headers.get('authorization')) {\n            try {\n                const { user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n                if (user) {\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_2__.systemLogDb.create({\n                        action: 'DEPOSIT_VERIFICATION_ERROR',\n                        userId: user.id,\n                        details: {\n                            error: error instanceof Error ? error.message : 'Unknown error'\n                        },\n                        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n                        userAgent: request.headers.get('user-agent') || 'unknown'\n                    });\n                }\n            } catch (logError) {\n                console.error('Failed to log error:', logError);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to verify deposit. Please try again later.'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wallet/deposit/verify/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30d';\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, 12);\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    },\n    async updatePassword (id, hashedPassword) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                password: hashedPassword\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async findVerified (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: true,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/depositVerificationService.ts":
/*!***********************************************!*\
  !*** ./src/lib/depositVerificationService.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DepositVerificationService: () => (/* binding */ DepositVerificationService),\n/* harmony export */   depositVerificationService: () => (/* binding */ depositVerificationService)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_trongrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/trongrid */ \"(rsc)/./src/lib/trongrid.ts\");\n\n\n\n// Map to track active verification processes to prevent duplicates\nconst activeVerifications = new Map();\n// Map to track confirmation checking intervals\nconst confirmationIntervals = new Map();\n/**\n * Background service for automated USDT deposit verification\n */ class DepositVerificationService {\n    static getInstance() {\n        if (!DepositVerificationService.instance) {\n            DepositVerificationService.instance = new DepositVerificationService();\n        }\n        return DepositVerificationService.instance;\n    }\n    /**\n   * Start the background verification service\n   */ async start() {\n        if (this.isRunning) {\n            console.log('Deposit verification service is already running');\n            return;\n        }\n        this.isRunning = true;\n        console.log('Starting deposit verification service...');\n        // Process existing pending verification deposits\n        await this.processPendingVerifications();\n        // Process existing waiting for confirmations deposits\n        await this.processWaitingForConfirmations();\n        console.log('Deposit verification service started successfully');\n    }\n    /**\n   * Stop the background verification service\n   */ stop() {\n        this.isRunning = false;\n        // Clear all active intervals\n        confirmationIntervals.forEach((interval)=>{\n            clearTimeout(interval);\n        });\n        confirmationIntervals.clear();\n        activeVerifications.clear();\n        console.log('Deposit verification service stopped');\n    }\n    /**\n   * Process deposits with PENDING_VERIFICATION status\n   */ async processPendingVerifications() {\n        try {\n            const pendingDeposits = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.getPendingVerificationDeposits();\n            console.log(`Found ${pendingDeposits.length} deposits pending verification`);\n            for (const deposit of pendingDeposits){\n                if (!activeVerifications.has(deposit.transactionId)) {\n                    this.scheduleVerification(deposit.transactionId, deposit.tronAddress);\n                }\n            }\n        } catch (error) {\n            console.error('Error processing pending verifications:', error);\n            await _lib_database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'DEPOSIT_VERIFICATION_ERROR',\n                details: `Error processing pending verifications: ${error instanceof Error ? error.message : 'Unknown error'}`\n            });\n        }\n    }\n    /**\n   * Process deposits with WAITING_FOR_CONFIRMATIONS status\n   */ async processWaitingForConfirmations() {\n        try {\n            const waitingDeposits = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.getWaitingForConfirmationsDeposits();\n            console.log(`Found ${waitingDeposits.length} deposits waiting for confirmations`);\n            for (const deposit of waitingDeposits){\n                if (!confirmationIntervals.has(deposit.transactionId)) {\n                    this.scheduleConfirmationCheck(deposit.transactionId, deposit.tronAddress);\n                }\n            }\n        } catch (error) {\n            console.error('Error processing waiting for confirmations:', error);\n            await _lib_database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'CONFIRMATION_CHECK_ERROR',\n                details: `Error processing waiting for confirmations: ${error instanceof Error ? error.message : 'Unknown error'}`\n            });\n        }\n    }\n    /**\n   * Schedule verification for a transaction (with 60-second retry)\n   */ scheduleVerification(transactionId, tronAddress) {\n        if (activeVerifications.has(transactionId)) {\n            return; // Already being processed\n        }\n        activeVerifications.set(transactionId, true);\n        console.log(`Scheduling verification for transaction: ${transactionId}`);\n        // Immediate verification attempt\n        this.verifyTransaction(transactionId, tronAddress, false);\n        // Schedule retry after 60 seconds if not found\n        setTimeout(()=>{\n            this.verifyTransaction(transactionId, tronAddress, true);\n        }, 60000);\n    }\n    /**\n   * Verify a single transaction\n   */ async verifyTransaction(transactionId, tronAddress, isRetry) {\n        try {\n            console.log(`${isRetry ? 'Retrying' : 'Attempting'} verification for transaction: ${transactionId}`);\n            // Get current deposit address from admin settings (always use the latest configured address)\n            let currentDepositAddress = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('usdtDepositAddress');\n            if (!currentDepositAddress) {\n                currentDepositAddress = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');\n            }\n            // Clean deposit address - remove quotes and extra characters\n            if (currentDepositAddress) {\n                currentDepositAddress = currentDepositAddress.replace(/['\"]/g, '').trim();\n            }\n            // Use the current admin-configured deposit address instead of the stored one\n            const addressToVerify = currentDepositAddress || tronAddress;\n            console.log(`Using deposit address for verification: ${addressToVerify} (from admin settings: ${!!currentDepositAddress})`);\n            // Get minimum confirmations setting\n            const minConfirmations = parseInt(await _lib_database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('minConfirmations') || '10');\n            // Get deposit settings for validation\n            const minDepositAmount = parseFloat(await _lib_database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('minDepositAmount') || '10');\n            const maxDepositAmount = parseFloat(await _lib_database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('maxDepositAmount') || '10000');\n            // Verify the transaction with timeout using the current deposit address\n            const verificationPromise = (0,_lib_trongrid__WEBPACK_IMPORTED_MODULE_2__.verifyUSDTTransaction)(transactionId, addressToVerify, 1);\n            const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error('Verification timeout')), 30000));\n            const verificationResult = await Promise.race([\n                verificationPromise,\n                timeoutPromise\n            ]);\n            if (!verificationResult.isValid && verificationResult.confirmations === 0) {\n                if (isRetry) {\n                    // Final attempt failed - mark as failed\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n                        failureReason: 'Transaction not found or invalid after verification attempts',\n                        processedAt: new Date()\n                    });\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                        action: 'DEPOSIT_VERIFICATION_FAILED',\n                        details: `Transaction ${transactionId} failed verification after retry`\n                    });\n                    activeVerifications.delete(transactionId);\n                }\n                return;\n            }\n            // Validate recipient address using the current admin-configured address\n            const hasValidRecipient = verificationResult.toAddress.toLowerCase().includes(addressToVerify.toLowerCase().slice(1, 10)) || addressToVerify.toLowerCase().includes(verificationResult.toAddress.toLowerCase().slice(1, 10));\n            if (!hasValidRecipient) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n                    failureReason: 'Invalid recipient address',\n                    processedAt: new Date()\n                });\n                activeVerifications.delete(transactionId);\n                return;\n            }\n            // Validate deposit amount\n            if (verificationResult.amount < minDepositAmount) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n                    failureReason: `Deposit amount ${verificationResult.amount} USDT is below minimum ${minDepositAmount} USDT`,\n                    processedAt: new Date()\n                });\n                activeVerifications.delete(transactionId);\n                return;\n            }\n            if (verificationResult.amount > maxDepositAmount) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n                    failureReason: `Deposit amount ${verificationResult.amount} USDT exceeds maximum ${maxDepositAmount} USDT`,\n                    processedAt: new Date()\n                });\n                activeVerifications.delete(transactionId);\n                return;\n            }\n            // Transaction found and validated - update with verification details\n            // First update the deposit record with transaction details\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.depositTransaction.update({\n                where: {\n                    transactionId\n                },\n                data: {\n                    amount: verificationResult.amount,\n                    usdtAmount: verificationResult.amount,\n                    senderAddress: verificationResult.fromAddress,\n                    blockNumber: verificationResult.blockNumber.toString(),\n                    blockTimestamp: new Date(verificationResult.blockTimestamp),\n                    confirmations: verificationResult.confirmations,\n                    tronAddress: addressToVerify\n                }\n            });\n            await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateStatus(transactionId, 'PENDING', {\n                confirmations: verificationResult.confirmations\n            });\n            console.log(`Transaction ${transactionId} verified with ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);\n            // Check if it has enough confirmations\n            if (verificationResult.confirmations >= minConfirmations) {\n                await this.completeDeposit(transactionId, verificationResult.amount);\n            } else {\n                // Not enough confirmations - start confirmation checking\n                await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateStatus(transactionId, 'WAITING_FOR_CONFIRMATIONS');\n                this.scheduleConfirmationCheck(transactionId, addressToVerify);\n            }\n            activeVerifications.delete(transactionId);\n        } catch (error) {\n            console.error(`Error verifying transaction ${transactionId}:`, error);\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            const isNetworkError = errorMessage.includes('timeout') || errorMessage.includes('network') || errorMessage.includes('ECONNRESET');\n            if (isRetry || !isNetworkError) {\n                // Final attempt failed due to error or non-network error\n                await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n                    failureReason: `Verification error: ${errorMessage}`,\n                    processedAt: new Date()\n                });\n                activeVerifications.delete(transactionId);\n                await _lib_database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                    action: 'DEPOSIT_VERIFICATION_FAILED',\n                    details: `Transaction ${transactionId} failed verification: ${errorMessage}`\n                });\n            } else {\n                // Network error on first attempt - will retry in 60 seconds\n                await _lib_database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                    action: 'DEPOSIT_VERIFICATION_NETWORK_ERROR',\n                    details: `Network error verifying transaction ${transactionId}: ${errorMessage}. Will retry.`\n                });\n            }\n        }\n    }\n    /**\n   * Schedule confirmation checking for a transaction\n   */ scheduleConfirmationCheck(transactionId, tronAddress) {\n        if (confirmationIntervals.has(transactionId)) {\n            return; // Already being checked\n        }\n        console.log(`Starting confirmation checking for transaction: ${transactionId}`);\n        const interval = setInterval(async ()=>{\n            await this.checkConfirmations(transactionId, tronAddress);\n        }, 60000); // Check every 60 seconds\n        confirmationIntervals.set(transactionId, interval);\n        // Also check immediately\n        this.checkConfirmations(transactionId, tronAddress);\n    }\n    /**\n   * Check confirmations for a transaction\n   */ async checkConfirmations(transactionId, tronAddress) {\n        try {\n            console.log(`Checking confirmations for transaction: ${transactionId}`);\n            // Get current deposit address from admin settings (always use the latest configured address)\n            let currentDepositAddress = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('usdtDepositAddress');\n            if (!currentDepositAddress) {\n                currentDepositAddress = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');\n            }\n            // Clean deposit address - remove quotes and extra characters\n            if (currentDepositAddress) {\n                currentDepositAddress = currentDepositAddress.replace(/['\"]/g, '').trim();\n            }\n            // Use the current admin-configured deposit address instead of the stored one\n            const addressToVerify = currentDepositAddress || tronAddress;\n            // Get minimum confirmations setting\n            const minConfirmations = parseInt(await _lib_database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('minConfirmations') || '10');\n            // Re-verify to get current confirmation count using the current deposit address\n            const verificationResult = await (0,_lib_trongrid__WEBPACK_IMPORTED_MODULE_2__.verifyUSDTTransaction)(transactionId, addressToVerify, 1);\n            if (!verificationResult.isValid) {\n                console.log(`Transaction ${transactionId} is no longer valid during confirmation check`);\n                return;\n            }\n            // Update confirmation count\n            await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateConfirmations(transactionId, verificationResult.confirmations);\n            console.log(`Transaction ${transactionId} has ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);\n            // Check if it now has enough confirmations\n            if (verificationResult.confirmations >= minConfirmations) {\n                await this.completeDeposit(transactionId, verificationResult.amount);\n                // Stop checking confirmations for this transaction\n                const interval = confirmationIntervals.get(transactionId);\n                if (interval) {\n                    clearInterval(interval);\n                    confirmationIntervals.delete(transactionId);\n                }\n            }\n        } catch (error) {\n            console.error(`Error checking confirmations for transaction ${transactionId}:`, error);\n            await _lib_database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'CONFIRMATION_CHECK_ERROR',\n                details: `Error checking confirmations for ${transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`\n            });\n        }\n    }\n    /**\n   * Complete a deposit by crediting the user's wallet\n   */ async completeDeposit(transactionId, amount) {\n        try {\n            console.log(`Completing deposit for transaction: ${transactionId} with amount: ${amount}`);\n            // Get deposit record to get user ID\n            const deposit = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.findByTransactionId(transactionId);\n            if (!deposit) {\n                throw new Error('Deposit record not found');\n            }\n            // Check if deposit is already completed to prevent double processing\n            if (deposit.status === 'CONFIRMED' || deposit.status === 'COMPLETED') {\n                console.log(`Deposit ${transactionId} already completed, skipping...`);\n                return;\n            }\n            // Use database transaction to ensure atomicity\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n                // Update deposit status to confirmed\n                await tx.depositTransaction.update({\n                    where: {\n                        transactionId\n                    },\n                    data: {\n                        status: 'CONFIRMED',\n                        verifiedAt: new Date(),\n                        processedAt: new Date()\n                    }\n                });\n                // Get current wallet balance\n                const currentWallet = await tx.walletBalance.findUnique({\n                    where: {\n                        userId: deposit.userId\n                    }\n                });\n                if (!currentWallet) {\n                    // Create wallet if it doesn't exist\n                    await tx.walletBalance.create({\n                        data: {\n                            userId: deposit.userId,\n                            availableBalance: amount,\n                            pendingBalance: 0,\n                            totalDeposits: amount,\n                            totalWithdrawals: 0,\n                            totalEarnings: 0\n                        }\n                    });\n                } else {\n                    // Update existing wallet\n                    await tx.walletBalance.update({\n                        where: {\n                            userId: deposit.userId\n                        },\n                        data: {\n                            availableBalance: currentWallet.availableBalance + amount,\n                            totalDeposits: currentWallet.totalDeposits + amount,\n                            lastUpdated: new Date()\n                        }\n                    });\n                }\n                // Check if there's already a pending transaction for this deposit\n                const existingTransaction = await tx.transaction.findFirst({\n                    where: {\n                        userId: deposit.userId,\n                        type: 'DEPOSIT',\n                        description: `USDT TRC20 Deposit - TX: ${transactionId}`,\n                        status: 'PENDING'\n                    }\n                });\n                if (existingTransaction) {\n                    // Update existing pending transaction to completed\n                    await tx.transaction.update({\n                        where: {\n                            id: existingTransaction.id\n                        },\n                        data: {\n                            status: 'COMPLETED',\n                            amount: amount\n                        }\n                    });\n                } else {\n                    // Create new transaction record for balance tracking (fallback)\n                    await tx.transaction.create({\n                        data: {\n                            userId: deposit.userId,\n                            type: 'DEPOSIT',\n                            amount: amount,\n                            description: `USDT TRC20 Deposit - TX: ${transactionId}`,\n                            status: 'COMPLETED'\n                        }\n                    });\n                }\n            });\n            await _lib_database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'DEPOSIT_COMPLETED',\n                userId: deposit.userId,\n                details: `Deposit completed: ${amount} USDT from transaction ${transactionId}`\n            });\n            // Send email notification\n            try {\n                const { emailNotificationService } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/nodemailer\"), __webpack_require__.e(\"_rsc_src_lib_emailNotificationService_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./emailNotificationService */ \"(rsc)/./src/lib/emailNotificationService.ts\"));\n                const user = await userDb.findById(deposit.userId);\n                if (user) {\n                    await emailNotificationService.sendDepositSuccessNotification({\n                        userId: user.id,\n                        email: user.email,\n                        firstName: user.firstName,\n                        lastName: user.lastName,\n                        amount,\n                        transactionId,\n                        currency: 'USDT'\n                    });\n                }\n            } catch (emailError) {\n                console.error('Error sending deposit success email:', emailError);\n            // Don't fail the deposit completion if email fails\n            }\n            console.log(`Deposit completed successfully for transaction: ${transactionId}`);\n        } catch (error) {\n            console.error(`Error completing deposit for transaction ${transactionId}:`, error);\n            // Mark as failed if completion fails\n            await _lib_database__WEBPACK_IMPORTED_MODULE_0__.depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n                failureReason: `Completion error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n                processedAt: new Date()\n            });\n            await _lib_database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'DEPOSIT_COMPLETION_ERROR',\n                details: `Error completing deposit ${transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`\n            });\n        }\n    }\n    /**\n   * Add a new transaction for verification\n   */ async addTransactionForVerification(transactionId, tronAddress) {\n        if (!this.isRunning) {\n            console.log('Deposit verification service is not running, starting verification manually');\n        }\n        this.scheduleVerification(transactionId, tronAddress);\n    }\n    /**\n   * Get service status\n   */ getStatus() {\n        return {\n            isRunning: this.isRunning,\n            activeVerifications: activeVerifications.size,\n            confirmationChecks: confirmationIntervals.size\n        };\n    }\n    constructor(){\n        this.isRunning = false;\n    }\n}\n// Export singleton instance\nconst depositVerificationService = DepositVerificationService.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/depositVerificationService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/trongrid.ts":
/*!*****************************!*\
  !*** ./src/lib/trongrid.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccountInfo: () => (/* binding */ getAccountInfo),\n/* harmony export */   getCurrentBlock: () => (/* binding */ getCurrentBlock),\n/* harmony export */   getCurrentNetworkConfig: () => (/* binding */ getCurrentNetworkConfig),\n/* harmony export */   getTransactionById: () => (/* binding */ getTransactionById),\n/* harmony export */   getTransactionInfo: () => (/* binding */ getTransactionInfo),\n/* harmony export */   isValidTronAddress: () => (/* binding */ isValidTronAddress),\n/* harmony export */   isValidTronTransactionId: () => (/* binding */ isValidTronTransactionId),\n/* harmony export */   verifyUSDTTransaction: () => (/* binding */ verifyUSDTTransaction)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var tron_format_address__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tron-format-address */ \"(rsc)/./node_modules/tron-format-address/build/lib/crypto.js\");\n/* harmony import */ var tron_format_address__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(tron_format_address__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Trongrid API Integration for USDT TRC20 Transaction Verification\n *\n * This module provides utilities to interact with the Trongrid API\n * to verify USDT TRC20 transactions on the Tron blockchain.\n * Supports both Mainnet and Testnet configurations.\n */ \n\nconst TRONGRID_API_KEY = process.env.TRONGRID_API_KEY; // Optional, for higher rate limits\n// Rate limiting configuration\nconst RATE_LIMIT_DELAY = 1000; // 1 second between requests\nlet lastRequestTime = 0;\n/**\n * Get current Tron network configuration from admin settings\n */ async function getTronNetworkConfig() {\n    try {\n        const network = await _database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('tronNetwork') || 'testnet';\n        let mainnetApiUrl = await _database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('tronMainnetApiUrl') || 'https://api.trongrid.io';\n        let testnetApiUrl = await _database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('tronTestnetApiUrl') || 'https://api.shasta.trongrid.io';\n        let mainnetContract = await _database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('usdtMainnetContract') || 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';\n        let testnetContract = await _database__WEBPACK_IMPORTED_MODULE_0__.adminSettingsDb.get('usdtTestnetContract') || 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs';\n        // Clean up URLs and contracts - remove quotes and extra characters\n        mainnetApiUrl = mainnetApiUrl.replace(/['\"]/g, '').trim();\n        testnetApiUrl = testnetApiUrl.replace(/['\"]/g, '').trim();\n        mainnetContract = mainnetContract.replace(/['\"]/g, '').trim();\n        testnetContract = testnetContract.replace(/['\"]/g, '').trim();\n        // Ensure URLs don't end with slash\n        mainnetApiUrl = mainnetApiUrl.replace(/\\/$/, '');\n        testnetApiUrl = testnetApiUrl.replace(/\\/$/, '');\n        const isMainnet = network === 'mainnet';\n        const finalApiUrl = isMainnet ? mainnetApiUrl : testnetApiUrl;\n        const finalContract = isMainnet ? mainnetContract : testnetContract;\n        console.log('Tron network config:', {\n            network,\n            apiUrl: finalApiUrl,\n            usdtContract: finalContract\n        });\n        return {\n            apiUrl: finalApiUrl,\n            usdtContract: finalContract,\n            network: network\n        };\n    } catch (error) {\n        console.error('Error getting Tron network config, using testnet defaults:', error);\n        // Fallback to testnet configuration\n        return {\n            apiUrl: 'https://api.shasta.trongrid.io',\n            usdtContract: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs',\n            network: 'testnet'\n        };\n    }\n}\n/**\n * Rate limiting helper to prevent API abuse\n */ async function rateLimitDelay() {\n    const now = Date.now();\n    const timeSinceLastRequest = now - lastRequestTime;\n    if (timeSinceLastRequest < RATE_LIMIT_DELAY) {\n        const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;\n        await new Promise((resolve)=>setTimeout(resolve, delay));\n    }\n    lastRequestTime = Date.now();\n}\n/**\n * Make HTTP request to Trongrid API with proper headers and error handling\n */ async function makeApiRequest(endpoint, networkConfig) {\n    await rateLimitDelay();\n    // Get network config if not provided\n    if (!networkConfig) {\n        networkConfig = await getTronNetworkConfig();\n    }\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    if (TRONGRID_API_KEY) {\n        headers['TRON-PRO-API-KEY'] = TRONGRID_API_KEY;\n    }\n    const response = await fetch(`${networkConfig.apiUrl}${endpoint}`, {\n        method: 'GET',\n        headers\n    });\n    if (!response.ok) {\n        throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);\n    }\n    return await response.json();\n}\n/**\n * Get transaction details by transaction ID\n */ async function getTransactionById(txId, networkConfig) {\n    try {\n        const config = networkConfig || await getTronNetworkConfig();\n        const url = `${config.apiUrl}/walletsolidity/gettransactionbyid`;\n        console.log('Fetching transaction by ID:', {\n            txId,\n            url,\n            network: config.network\n        });\n        const response = await fetch(url, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                value: txId\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Trongrid API error response:', {\n                status: response.status,\n                statusText: response.statusText,\n                body: errorText\n            });\n            throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        console.log('Transaction data received:', data);\n        return data.txID ? data : null;\n    } catch (error) {\n        console.error('Error fetching transaction:', {\n            txId,\n            error: error instanceof Error ? error.message : error,\n            stack: error instanceof Error ? error.stack : undefined\n        });\n        return null;\n    }\n}\n/**\n * Get transaction info (including receipt) by transaction ID\n */ async function getTransactionInfo(txId, networkConfig) {\n    try {\n        const config = networkConfig || await getTronNetworkConfig();\n        const url = `${config.apiUrl}/walletsolidity/gettransactioninfobyid`;\n        console.log('Fetching transaction info:', {\n            txId,\n            url,\n            network: config.network\n        });\n        const response = await fetch(url, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                value: txId\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Trongrid API error response:', {\n                status: response.status,\n                statusText: response.statusText,\n                body: errorText\n            });\n            throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        console.log('Transaction info received:', data);\n        return data.id ? data : null;\n    } catch (error) {\n        console.error('Error fetching transaction info:', {\n            txId,\n            error: error instanceof Error ? error.message : error,\n            stack: error instanceof Error ? error.stack : undefined\n        });\n        return null;\n    }\n}\n/**\n * Get current block number\n */ async function getCurrentBlock(networkConfig) {\n    try {\n        const config = networkConfig || await getTronNetworkConfig();\n        const url = `${config.apiUrl}/walletsolidity/getnowblock`;\n        console.log('Fetching current block:', {\n            url,\n            network: config.network\n        });\n        const response = await fetch(url, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Trongrid API error response:', {\n                status: response.status,\n                statusText: response.statusText,\n                body: errorText\n            });\n            throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);\n        }\n        const data = await response.json();\n        console.log('Current block data received:', data);\n        return data.block_header ? {\n            blockNumber: data.block_header.raw_data.number\n        } : null;\n    } catch (error) {\n        console.error('Error fetching current block:', {\n            error: error instanceof Error ? error.message : error,\n            stack: error instanceof Error ? error.stack : undefined\n        });\n        return null;\n    }\n}\n/**\n * Convert hex string to decimal number\n */ function hexToDecimal(hex) {\n    return parseInt(hex, 16);\n}\n/**\n * Convert Tron address from hex to base58\n */ function hexToTronAddress(hex) {\n    try {\n        // Remove '0x' prefix if present\n        let cleanHex = hex.startsWith('0x') ? hex.slice(2) : hex;\n        // Ensure we have a 40-character hex string (20 bytes)\n        if (cleanHex.length < 40) {\n            cleanHex = '0'.repeat(40 - cleanHex.length) + cleanHex;\n        }\n        // Add the Tron address prefix (0x41 for mainnet/testnet)\n        const addressHex = '41' + cleanHex;\n        // Convert to base58\n        return (0,tron_format_address__WEBPACK_IMPORTED_MODULE_1__.fromHex)(addressHex);\n    } catch (error) {\n        console.error('Error converting hex to Tron address:', error);\n        // Fallback to a recognizable format if conversion fails\n        return `T${hex.slice(-30)}`;\n    }\n}\n/**\n * Parse USDT TRC20 transfer from transaction data and logs\n */ function parseUSDTTransfer(transaction, logs, usdtContract) {\n    console.log('Parsing USDT transfer from transaction and logs:', logs.length, 'Contract:', usdtContract);\n    // First, try to parse from transaction data (more reliable for recipient address)\n    if (transaction?.raw_data?.contract?.[0]?.parameter?.value) {\n        const contractValue = transaction.raw_data.contract[0].parameter.value;\n        // Check if this is a USDT contract call\n        const contractAddressBase58 = hexToTronAddress(contractValue.contract_address?.replace('41', '') || '');\n        const isUSDTContract = contractAddressBase58.toLowerCase() === usdtContract.toLowerCase();\n        if (isUSDTContract && contractValue.data) {\n            try {\n                const data = contractValue.data;\n                // Check if this is a transfer function call (a9059cbb = transfer)\n                if (data.startsWith('a9059cbb')) {\n                    console.log('Found USDT transfer in transaction data');\n                    // Parse recipient address from data (next 64 chars after function selector)\n                    const recipientHex = data.slice(8, 72).slice(24); // Remove padding\n                    const toAddress = hexToTronAddress(recipientHex);\n                    // Parse amount from data (last 64 chars)\n                    const amountHex = data.slice(72);\n                    const amount = hexToDecimal(amountHex) / 1000000; // USDT has 6 decimals\n                    // Get sender address from transaction\n                    const fromAddress = hexToTronAddress(contractValue.owner_address?.replace('41', '') || '');\n                    console.log('Parsed transfer from transaction data:', {\n                        fromAddress,\n                        toAddress,\n                        amount,\n                        recipientHex,\n                        amountHex\n                    });\n                    return {\n                        amount,\n                        fromAddress,\n                        toAddress\n                    };\n                }\n            } catch (error) {\n                console.error('Error parsing transaction data:', error);\n            }\n        }\n    }\n    // Fallback to parsing from logs if transaction data parsing fails\n    const usdtLog = logs.find((log)=>{\n        const logAddressBase58 = hexToTronAddress(log.address);\n        const isUSDTContract = logAddressBase58.toLowerCase() === usdtContract.toLowerCase();\n        const hasCorrectTopics = log.topics.length >= 3;\n        const isTransferEvent = log.topics[0] === 'ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';\n        return isUSDTContract && hasCorrectTopics && isTransferEvent;\n    });\n    if (!usdtLog) {\n        console.log('No USDT transfer found in transaction data or logs');\n        return null;\n    }\n    try {\n        console.log('Parsing from logs as fallback:', usdtLog);\n        const fromAddressHex = usdtLog.topics[1].slice(26);\n        const toAddressHex = usdtLog.topics[2].slice(26);\n        const amountHex = usdtLog.data.startsWith('0x') ? usdtLog.data.slice(2) : usdtLog.data;\n        const fromAddress = hexToTronAddress(fromAddressHex);\n        const toAddress = hexToTronAddress(toAddressHex);\n        const amount = hexToDecimal(amountHex) / 1000000;\n        return {\n            amount,\n            fromAddress,\n            toAddress\n        };\n    } catch (error) {\n        console.error('Error parsing USDT transfer from logs:', error);\n        return null;\n    }\n}\n/**\n * Verify USDT TRC20 transaction and extract transfer details\n */ async function verifyUSDTTransaction(txId, expectedToAddress, minConfirmations = 1) {\n    // Get current network configuration\n    const networkConfig = await getTronNetworkConfig();\n    console.log('Verifying USDT transaction:', {\n        txId,\n        expectedToAddress,\n        minConfirmations,\n        network: networkConfig.network,\n        apiUrl: networkConfig.apiUrl,\n        usdtContract: networkConfig.usdtContract\n    });\n    try {\n        // Get transaction details\n        const transaction = await getTransactionById(txId, networkConfig);\n        console.log('Transaction details:', transaction);\n        if (!transaction) {\n            console.log('Transaction not found');\n            return {\n                isValid: false,\n                amount: 0,\n                fromAddress: '',\n                toAddress: '',\n                contractAddress: networkConfig.usdtContract,\n                blockNumber: 0,\n                blockTimestamp: 0,\n                confirmations: 0,\n                transactionId: txId\n            };\n        }\n        // Get transaction info for receipt and confirmations\n        const transactionInfo = await getTransactionInfo(txId, networkConfig);\n        console.log('Transaction info:', transactionInfo);\n        if (!transactionInfo) {\n            console.log('Transaction info not found');\n            return {\n                isValid: false,\n                amount: 0,\n                fromAddress: '',\n                toAddress: '',\n                contractAddress: networkConfig.usdtContract,\n                blockNumber: 0,\n                blockTimestamp: 0,\n                confirmations: 0,\n                transactionId: txId\n            };\n        }\n        // Check if transaction was successful\n        console.log('Transaction receipt result:', transactionInfo.receipt?.result);\n        if (transactionInfo.receipt?.result !== 'SUCCESS') {\n            console.log('Transaction failed or not successful');\n            return {\n                isValid: false,\n                amount: 0,\n                fromAddress: '',\n                toAddress: '',\n                contractAddress: networkConfig.usdtContract,\n                blockNumber: transactionInfo.blockNumber,\n                blockTimestamp: transactionInfo.blockTimeStamp,\n                confirmations: 0,\n                transactionId: txId\n            };\n        }\n        // Parse USDT transfer from transaction data and logs\n        const transferDetails = parseUSDTTransfer(transaction, transactionInfo.log || [], networkConfig.usdtContract);\n        if (!transferDetails) {\n            console.log('No USDT transfer details found');\n            return {\n                isValid: false,\n                amount: 0,\n                fromAddress: '',\n                toAddress: '',\n                contractAddress: networkConfig.usdtContract,\n                blockNumber: transactionInfo.blockNumber,\n                blockTimestamp: transactionInfo.blockTimeStamp,\n                confirmations: 0,\n                transactionId: txId\n            };\n        }\n        // Calculate confirmations using block numbers\n        const currentBlock = await getCurrentBlock(networkConfig);\n        let confirmations = 0;\n        if (currentBlock && transactionInfo.blockNumber) {\n            confirmations = currentBlock.blockNumber - transactionInfo.blockNumber;\n        }\n        console.log('Confirmation calculation:', {\n            currentBlockNumber: currentBlock?.blockNumber,\n            transactionBlockNumber: transactionInfo.blockNumber,\n            confirmations,\n            minConfirmations\n        });\n        // Verify the recipient address matches expected address\n        const isValidRecipient = transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase();\n        console.log('Address verification:', {\n            transferToAddress: transferDetails.toAddress,\n            expectedToAddress,\n            isValidRecipient,\n            addressesMatch: transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase()\n        });\n        const isValid = isValidRecipient && confirmations >= minConfirmations && transferDetails.amount > 0;\n        console.log('Final verification result:', {\n            isValid,\n            isValidRecipient,\n            confirmations,\n            minConfirmations,\n            amount: transferDetails.amount\n        });\n        return {\n            isValid,\n            amount: transferDetails.amount,\n            fromAddress: transferDetails.fromAddress,\n            toAddress: transferDetails.toAddress,\n            contractAddress: networkConfig.usdtContract,\n            blockNumber: transactionInfo.blockNumber,\n            blockTimestamp: transactionInfo.blockTimeStamp,\n            confirmations: Math.max(0, confirmations),\n            transactionId: txId\n        };\n    } catch (error) {\n        console.error('Error verifying USDT transaction:', error);\n        return {\n            isValid: false,\n            amount: 0,\n            fromAddress: '',\n            toAddress: '',\n            contractAddress: networkConfig.usdtContract,\n            blockNumber: 0,\n            blockTimestamp: 0,\n            confirmations: 0,\n            transactionId: txId\n        };\n    }\n}\n/**\n * Validate Tron transaction ID format\n */ function isValidTronTransactionId(txId) {\n    // Tron transaction IDs are 64-character hexadecimal strings\n    const tronTxRegex = /^[a-fA-F0-9]{64}$/;\n    return tronTxRegex.test(txId);\n}\n/**\n * Validate Tron address format\n */ function isValidTronAddress(address) {\n    // Tron addresses start with 'T' and are 34 characters long\n    const tronAddressRegex = /^T[A-Za-z1-9]{33}$/;\n    return tronAddressRegex.test(address);\n}\n/**\n * Get account information by address\n */ async function getAccountInfo(address, networkConfig) {\n    try {\n        const data = await makeApiRequest(`/v1/accounts/${address}`, networkConfig);\n        return data.data && data.data.length > 0 ? data.data[0] : null;\n    } catch (error) {\n        console.error('Error fetching account info:', error);\n        return null;\n    }\n}\n/**\n * Get current network configuration (exported for external use)\n */ async function getCurrentNetworkConfig() {\n    return await getTronNetworkConfig();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/trongrid.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/tron-format-address"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute&page=%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwallet%2Fdeposit%2Fverify%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();