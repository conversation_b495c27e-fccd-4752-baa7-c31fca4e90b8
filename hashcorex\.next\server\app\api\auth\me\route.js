/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/me/route";
exports.ids = ["app/api/auth/me/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_auth_me_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/me/route.ts */ \"(rsc)/./src/app/api/auth/me/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/me/route\",\n        pathname: \"/api/auth/me\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/me/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\auth\\\\me\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_auth_me_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhdXRoJTJGbWUlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmF1dGglMkZtZSUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmF1dGglMkZtZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNkcmVhbSU1Q0Rlc2t0b3AlNUNIYXNoX01pbmluZ3MlNUNoYXNoY29yZXglNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2RyZWFtJTVDRGVza3RvcCU1Q0hhc2hfTWluaW5ncyU1Q2hhc2hjb3JleCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDcUM7QUFDbEg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhdXRoXFxcXG1lXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hdXRoL21lL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYXV0aC9tZVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYXV0aC9tZS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhdXRoXFxcXG1lXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/me/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/auth/me/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                id: user.id,\n                email: user.email,\n                firstName: user.firstName,\n                lastName: user.lastName,\n                referralId: user.referralId,\n                role: user.role,\n                kycStatus: user.kycStatus,\n                isActive: user.isActive,\n                createdAt: user.createdAt\n            }\n        });\n    } catch (error) {\n        console.error('Auth check error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Authentication check failed'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/me/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30d';\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, 12);\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();