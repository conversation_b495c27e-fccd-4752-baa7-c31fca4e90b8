import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { systemSettingsDb } from '@/lib/database';
import { emailService } from '@/lib/email';
import { ErrorLogger } from '@/lib/errorLogger';

// GET - Get email settings
export async function GET(request: NextRequest) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get email settings
    const emailSettings = await systemSettingsDb.getEmailSettings();

    // Don't return password in response
    const safeSettings = {
      ...emailSettings,
      smtpPassword: emailSettings.smtpPassword ? '••••••••' : '',
    };

    return NextResponse.json({
      success: true,
      data: safeSettings,
    });

  } catch (error) {
    console.error('Get email settings error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'GET_EMAIL_SETTINGS_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update email settings
export async function PUT(request: NextRequest) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const {
      smtpHost,
      smtpPort,
      smtpSecure,
      smtpUser,
      smtpPassword,
      fromName,
      fromEmail,
      emailEnabled,
    } = await request.json();

    // Validation
    if (!smtpHost || !smtpUser || !fromEmail) {
      return NextResponse.json(
        { success: false, error: 'SMTP Host, User, and From Email are required' },
        { status: 400 }
      );
    }

    // Prepare settings update
    const emailSettings: any = {
      smtpHost,
      smtpPort: parseInt(smtpPort) || 587,
      smtpSecure: Boolean(smtpSecure),
      smtpUser,
      fromName: fromName || 'HashCoreX',
      fromEmail,
      emailEnabled: Boolean(emailEnabled),
    };

    // Only update password if provided
    if (smtpPassword && smtpPassword !== '••••••••') {
      emailSettings.smtpPassword = smtpPassword;
    }

    // Update settings
    await systemSettingsDb.updateEmailSettings(emailSettings);

    return NextResponse.json({
      success: true,
      message: 'Email settings updated successfully',
    });

  } catch (error) {
    console.error('Update email settings error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'UPDATE_EMAIL_SETTINGS_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Test email connection
export async function POST(request: NextRequest) {
  try {
    // Authenticate admin
    const { authenticated, user } = await authenticateRequest(request);
    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isUserAdmin = await isAdmin(user.id);
    if (!isUserAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { testEmail } = await request.json();

    if (!testEmail) {
      return NextResponse.json(
        { success: false, error: 'Test email address is required' },
        { status: 400 }
      );
    }

    // Test email connection
    try {
      const connectionTest = await emailService.testConnection();

      if (!connectionTest) {
        return NextResponse.json(
          { success: false, error: 'Email connection test failed. Please check your SMTP settings.' },
          { status: 400 }
        );
      }
    } catch (connectionError) {
      console.error('Email connection test error:', connectionError);
      return NextResponse.json(
        { success: false, error: `Email connection failed: ${connectionError instanceof Error ? connectionError.message : 'Unknown error'}` },
        { status: 400 }
      );
    }

    // Send test email
    const testEmailSent = await emailService.sendEmail({
      to: testEmail,
      subject: 'HashCoreX Email Test',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #ffd60a;">HashCoreX Email Test</h2>
          <p>This is a test email to verify your SMTP configuration.</p>
          <p>If you received this email, your email settings are working correctly!</p>
          <p>Sent at: ${new Date().toISOString()}</p>
        </div>
      `,
      text: 'HashCoreX Email Test - If you received this email, your email settings are working correctly!',
    });

    if (testEmailSent) {
      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send test email' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Test email error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'TEST_EMAIL_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
