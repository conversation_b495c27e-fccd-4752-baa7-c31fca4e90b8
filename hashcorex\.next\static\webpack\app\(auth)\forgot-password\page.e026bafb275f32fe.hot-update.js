"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/forgot-password/page",{

/***/ "(app-pages-browser)/./src/app/(auth)/forgot-password/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(auth)/forgot-password/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ForgotPasswordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons */ \"(app-pages-browser)/./src/components/icons/index.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Eye,EyeOff,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/AuthGuard */ \"(app-pages-browser)/./src/components/auth/AuthGuard.tsx\");\n/* harmony import */ var _components_auth_OTPVerification__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/auth/OTPVerification */ \"(app-pages-browser)/./src/components/auth/OTPVerification.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ForgotPasswordForm() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newPassword, setNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const passwordValidation = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.validatePassword)(newPassword);\n    const handleEmailSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/send-otp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    purpose: 'password_reset'\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setStep('otp');\n            } else {\n                setError(data.error || 'Failed to send reset code');\n            }\n        } catch (error) {\n            setError('Failed to send reset code. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleOTPVerified = async (otp)=>{\n        setError('');\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/verify-otp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    otp,\n                    purpose: 'password_reset'\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setStep('password');\n            } else {\n                setError(data.error || 'Invalid OTP');\n            }\n        } catch (error) {\n            setError('Failed to verify OTP. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleResendOTP = async ()=>{\n        setError('');\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/send-otp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    purpose: 'password_reset'\n                })\n            });\n            const data = await response.json();\n            if (!data.success) {\n                setError(data.error || 'Failed to resend code');\n            }\n        } catch (error) {\n            setError('Failed to resend code. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordReset = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (newPassword !== confirmPassword) {\n            setError('Passwords do not match');\n            return;\n        }\n        if (!passwordValidation.isValid) {\n            setError('Please ensure your password meets all requirements');\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/auth/reset-password', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    newPassword,\n                    confirmPassword\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSuccess('Password reset successful! Redirecting to dashboard...');\n                setTimeout(()=>{\n                    router.push('/dashboard');\n                }, 2000);\n            } else {\n                setError(data.error || 'Failed to reset password');\n            }\n        } catch (error) {\n            setError('Failed to reset password. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex items-center justify-center p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-xl p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_5__.SolarPanel, {\n                                                className: \"h-10 w-10 text-solar-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-2xl font-bold text-gray-900\",\n                                                children: \"HashCoreX\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                        children: [\n                                            step === 'email' && 'Reset Password',\n                                            step === 'otp' && 'Verify Email',\n                                            step === 'password' && 'Set New Password'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            step === 'email' && 'Enter your email to receive a reset code',\n                                            step === 'otp' && 'Enter the verification code sent to your email',\n                                            step === 'password' && 'Create a new secure password'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 text-sm\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600 text-sm\",\n                                    children: success\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, this),\n                            step === 'email' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleEmailSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        label: \"Email Address\",\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Enter your email address\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        variant: \"primary\",\n                                        size: \"xl\",\n                                        className: \"w-full font-bold\",\n                                        loading: loading,\n                                        children: loading ? 'Sending Code...' : 'Send Reset Code'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this),\n                            step === 'otp' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_OTPVerification__WEBPACK_IMPORTED_MODULE_8__.OTPVerification, {\n                                    email: email,\n                                    onVerified: handleOTPVerified,\n                                    onResend: handleResendOTP,\n                                    loading: loading,\n                                    error: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this),\n                            step === 'password' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handlePasswordReset,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        label: \"New Password\",\n                                        type: showPassword ? 'text' : 'password',\n                                        value: newPassword,\n                                        onChange: (e)=>setNewPassword(e.target.value),\n                                        placeholder: \"Enter new password\",\n                                        required: true,\n                                        rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 39\n                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 72\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        label: \"Confirm New Password\",\n                                        type: showConfirmPassword ? 'text' : 'password',\n                                        value: confirmPassword,\n                                        onChange: (e)=>setConfirmPassword(e.target.value),\n                                        placeholder: \"Confirm new password\",\n                                        required: true,\n                                        rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 46\n                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 79\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    newPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Password Requirements:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: passwordValidation.checks.map((check, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 \".concat(check.valid ? 'text-green-500' : 'text-gray-300')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm \".concat(check.valid ? 'text-green-600' : 'text-gray-500'),\n                                                                children: check.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        variant: \"primary\",\n                                        size: \"xl\",\n                                        className: \"w-full font-bold\",\n                                        loading: loading,\n                                        disabled: !passwordValidation.isValid || newPassword !== confirmPassword,\n                                        children: loading ? 'Resetting Password...' : 'Reset Password'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"Remember your password?\",\n                                        ' ',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            className: \"text-solar-500 hover:text-solar-600 font-medium\",\n                                            children: \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 text-center lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center text-gray-500 hover:text-gray-700 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Back to Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block lg:flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-solar-600 to-blue-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black bg-opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-full flex items-center justify-center p-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6\",\n                                        children: \"Secure Password Reset\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-blue-100 mb-8\",\n                                        children: \"Reset your password securely with email verification\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-6 max-w-sm mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Eye_EyeOff_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Email Verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-100\",\n                                                            children: \"Secure OTP verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(ForgotPasswordForm, \"bKC5wjPrE9+ejvFAwVYdpDmgfvU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ForgotPasswordForm;\nfunction ForgotPasswordPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_7__.GuestRoute, {\n        redirectTo: \"/dashboard\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ForgotPasswordForm, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n            lineNumber: 360,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\forgot-password\\\\page.tsx\",\n        lineNumber: 359,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ForgotPasswordPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ForgotPasswordForm\");\n$RefreshReg$(_c1, \"ForgotPasswordPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(auth)/forgot-password/page.tsx\n"));

/***/ })

});