"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/UserProfileSettings.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProfileSettings: () => (/* binding */ UserProfileSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* __next_internal_client_entry_do_not_use__ UserProfileSettings auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst UserProfileSettings = ()=>{\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: true,\n        smsNotifications: false,\n        marketingEmails: false\n    });\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        referralId: ''\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordForm, setPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [withdrawalAddress, setWithdrawalAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadingPicture, setUploadingPicture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Message box hook\n    const { showMessage, MessageBoxComponent } = (0,_components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserProfileSettings.useEffect\": ()=>{\n            if (user) {\n                setProfileData({\n                    firstName: user.firstName || '',\n                    lastName: user.lastName || '',\n                    email: user.email || '',\n                    referralId: user.referralId || ''\n                });\n            }\n            fetchNotificationSettings();\n            fetchUserData();\n        }\n    }[\"UserProfileSettings.useEffect\"], [\n        user\n    ]);\n    const fetchNotificationSettings = async ()=>{\n        try {\n            const response = await fetch('/api/user/notification-settings', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setNotifications(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch notification settings:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch('/api/user/withdrawal-address', {\n                credentials: 'include'\n            });\n            const data = await response.json();\n            if (data.success) {\n                setWithdrawalAddress(data.data.withdrawalAddress || '');\n            }\n        } catch (error) {\n            console.error('Failed to fetch user data:', error);\n        }\n    };\n    const updateNotificationSettings = async (settings)=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/user/notification-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(settings)\n            });\n            if (response.ok) {\n                setNotifications(settings);\n            }\n        } catch (error) {\n            console.error('Failed to update notification settings:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const updateProfile = async ()=>{\n        try {\n            setSaving(true);\n            // Profile update API call would go here\n            console.log('Profile update:', profileData);\n        } catch (error) {\n            console.error('Failed to update profile:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const updatePassword = async ()=>{\n        if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n            showMessage({\n                title: 'Password Mismatch',\n                message: 'New passwords do not match',\n                variant: 'error'\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            // Password update API call would go here\n            console.log('Password update');\n            setPasswordForm({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Failed to update password:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleProfilePictureUpload = async (file)=>{\n        try {\n            var _user_refreshUser;\n            setUploadingPicture(true);\n            const formData = new FormData();\n            formData.append('profilePicture', file);\n            const response = await fetch('/api/user/profile-picture', {\n                method: 'POST',\n                credentials: 'include',\n                body: formData\n            });\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to upload profile picture');\n            }\n            // Refresh user data to get updated profile picture\n            await (user === null || user === void 0 ? void 0 : (_user_refreshUser = user.refreshUser) === null || _user_refreshUser === void 0 ? void 0 : _user_refreshUser.call(user));\n            showMessage({\n                title: 'Success',\n                message: 'Profile picture updated successfully',\n                variant: 'success'\n            });\n        } catch (error) {\n            console.error('Profile picture upload error:', error);\n            showMessage({\n                title: 'Upload Failed',\n                message: error instanceof Error ? error.message : 'Failed to upload profile picture',\n                variant: 'error'\n            });\n        } finally{\n            setUploadingPicture(false);\n        }\n    };\n    const handleProfilePictureRemove = async ()=>{\n        try {\n            var _user_refreshUser;\n            setUploadingPicture(true);\n            const response = await fetch('/api/user/profile-picture', {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to remove profile picture');\n            }\n            // Refresh user data to get updated profile picture\n            await (user === null || user === void 0 ? void 0 : (_user_refreshUser = user.refreshUser) === null || _user_refreshUser === void 0 ? void 0 : _user_refreshUser.call(user));\n            showMessage({\n                title: 'Success',\n                message: 'Profile picture removed successfully',\n                variant: 'success'\n            });\n        } catch (error) {\n            console.error('Profile picture removal error:', error);\n            showMessage({\n                title: 'Removal Failed',\n                message: error instanceof Error ? error.message : 'Failed to remove profile picture',\n                variant: 'error'\n            });\n        } finally{\n            setUploadingPicture(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, undefined);\n    }\n    const tabs = [\n        {\n            id: 'profile',\n            label: 'Profile Information',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security Settings',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notification Settings',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'billing',\n            label: 'Billing & Payments',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ];\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'profile':\n                return renderProfileContent();\n            case 'security':\n                return renderSecurityContent();\n            case 'notifications':\n                return renderNotificationContent();\n            case 'billing':\n                return renderBillingContent();\n            default:\n                return renderProfileContent();\n        }\n    };\n    const renderProfileContent = ()=>{\n        const isKycSubmittedOrApproved = (user === null || user === void 0 ? void 0 : user.kycStatus) === 'PENDING' || (user === null || user === void 0 ? void 0 : user.kycStatus) === 'APPROVED';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Profile Information\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-4 pb-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: \"Profile Picture\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.ProfilePictureUpload, {\n                                    currentPicture: user === null || user === void 0 ? void 0 : user.profilePicture,\n                                    onUpload: handleProfilePictureUpload,\n                                    onRemove: handleProfilePictureRemove,\n                                    loading: uploadingPicture,\n                                    disabled: saving\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, undefined),\n                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 font-medium\",\n                                            children: \"Profile names cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-1\",\n                                    children: \"Your identity has been verified and profile information is now locked for security.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"First Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"text\",\n                                            value: profileData.firstName,\n                                            onChange: (e)=>setProfileData((prev)=>({\n                                                        ...prev,\n                                                        firstName: e.target.value\n                                                    })),\n                                            disabled: isKycSubmittedOrApproved,\n                                            className: isKycSubmittedOrApproved ? \"bg-gray-100 border-gray-300 text-gray-500\" : \"bg-white border-gray-300 text-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Last Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"text\",\n                                            value: profileData.lastName,\n                                            onChange: (e)=>setProfileData((prev)=>({\n                                                        ...prev,\n                                                        lastName: e.target.value\n                                                    })),\n                                            disabled: isKycSubmittedOrApproved,\n                                            className: isKycSubmittedOrApproved ? \"bg-gray-100 border-gray-300 text-gray-500\" : \"bg-white border-gray-300 text-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"email\",\n                                    value: profileData.email,\n                                    disabled: true,\n                                    className: \"bg-gray-100 border-gray-300 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Email cannot be changed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Referral ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"text\",\n                                    value: profileData.referralId,\n                                    disabled: true,\n                                    className: \"bg-gray-100 border-gray-300 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Your unique referral identifier\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updateProfile,\n                            disabled: saving || isKycSubmittedOrApproved,\n                            className: \"w-full bg-yellow-500 hover:bg-yellow-600 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, undefined),\n                                saving ? 'Saving...' : isKycSubmittedOrApproved ? 'Profile Locked' : 'Save Profile'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 281,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderSecurityContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Security Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Current Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            value: passwordForm.currentPassword,\n                                            onChange: (e)=>setPasswordForm((prev)=>({\n                                                        ...prev,\n                                                        currentPassword: e.target.value\n                                                    })),\n                                            className: \"bg-white border-gray-300 text-gray-900 pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: passwordForm.newPassword,\n                                    onChange: (e)=>setPasswordForm((prev)=>({\n                                                ...prev,\n                                                newPassword: e.target.value\n                                            })),\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: passwordForm.confirmPassword,\n                                    onChange: (e)=>setPasswordForm((prev)=>({\n                                                ...prev,\n                                                confirmPassword: e.target.value\n                                            })),\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updatePassword,\n                            disabled: saving || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword,\n                            className: \"w-full bg-red-500 hover:bg-red-600 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 11\n                                }, undefined),\n                                saving ? 'Updating...' : 'Update Password'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 391,\n            columnNumber: 5\n        }, undefined);\n    const renderNotificationContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Notification Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Email Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Receive updates via email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.emailNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    emailNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Push Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Browser notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.pushNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    pushNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"SMS Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Text message alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.smsNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    smsNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 458,\n            columnNumber: 5\n        }, undefined);\n    const updateWithdrawalAddress = async ()=>{\n        try {\n            setSaving(true);\n            // Validate USDT TRC20 address format\n            if (withdrawalAddress && !withdrawalAddress.match(/^T[A-Za-z1-9]{33}$/)) {\n                showMessage({\n                    title: 'Invalid Address',\n                    message: 'Invalid USDT TRC20 address format. Address must start with \"T\" and be 34 characters long.',\n                    variant: 'error'\n                });\n                return;\n            }\n            const response = await fetch('/api/user/withdrawal-address', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    withdrawalAddress: withdrawalAddress.trim()\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Response not OK:', response.status, errorText);\n                throw new Error(\"Server error: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update withdrawal address');\n            }\n            // Show success message\n            showMessage({\n                title: 'Success',\n                message: 'Withdrawal address updated successfully!',\n                variant: 'success'\n            });\n        } catch (error) {\n            console.error('Failed to update withdrawal address:', error);\n            showMessage({\n                title: 'Error',\n                message: \"Error: \".concat(error.message),\n                variant: 'error'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const renderBillingContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Billing & Payments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Default Withdrawal Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"text\",\n                                    value: withdrawalAddress,\n                                    onChange: (e)=>setWithdrawalAddress(e.target.value),\n                                    placeholder: \"Enter your USDT TRC20 address\",\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"This address will be automatically filled in withdrawal forms. Only USDT TRC20 addresses are supported.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-800 mb-1\",\n                                                children: \"Security Notice\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: \"Your withdrawal address is encrypted and stored securely. You can update it anytime, but make sure to double-check the address as transactions cannot be reversed.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updateWithdrawalAddress,\n                            disabled: saving,\n                            className: \"w-full bg-yellow-500 hover:bg-yellow-600 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 11\n                                }, undefined),\n                                saving ? 'Saving...' : 'Save Withdrawal Address'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 592,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Profile & Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Manage your account information and preferences\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 644,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: tabs.map((tab)=>{\n                        const Icon = tab.icon;\n                        const isActive = activeTab === tab.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"\\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\\n                  \".concat(isActive ? 'border-yellow-500 text-yellow-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \"\\n                \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 650,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: renderTabContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 677,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBoxComponent, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 682,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n        lineNumber: 642,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UserProfileSettings, \"DGT74mfTk7N0X7+bUy0j1TqKodI=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox\n    ];\n});\n_c = UserProfileSettings;\nvar _c;\n$RefreshReg$(_c, \"UserProfileSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx\n"));

/***/ })

});