import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';
import { userDb } from './database';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30d';

// Password utilities
export const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, 12);
};

export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return await bcrypt.compare(password, hashedPassword);
};

// JWT utilities
export const generateToken = (payload: { userId: string; email: string }): string => {
  console.log('Generating token with payload:', payload);
  console.log('Using JWT_SECRET length:', JWT_SECRET.length);
  const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
  console.log('Generated token length:', token.length);
  return token;
};

export const verifyToken = (token: string): { userId: string; email: string } | null => {
  try {
    console.log('Verifying token with secret length:', JWT_SECRET.length);
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; email: string };
    console.log('Token verified successfully:', { userId: decoded.userId, email: decoded.email });
    return decoded;
  } catch (error) {
    console.log('Token verification failed:', error);
    return null;
  }
};

// Generate unique referral ID
export const generateReferralId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = 'HC'; // HashCoreX prefix
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Authentication middleware
export const authenticateRequest = async (request: NextRequest) => {
  const token = request.headers.get('authorization')?.replace('Bearer ', '') ||
                request.cookies.get('auth-token')?.value;

  if (!token) {
    return { authenticated: false, user: null };
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return { authenticated: false, user: null };
  }

  const user = await userDb.findByEmail(decoded.email);
  if (!user) {
    return { authenticated: false, user: null };
  }

  return { authenticated: true, user };
};

// User registration
export const registerUser = async (data: {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  referralCode?: string;
  placementSide?: 'left' | 'right';
}) => {
  // Check if user already exists
  const existingUser = await userDb.findByEmail(data.email);
  if (existingUser) {
    throw new Error('User already exists with this email');
  }

  // Validate referral code if provided
  let referrerId: string | undefined;
  if (data.referralCode) {
    const referrer = await userDb.findByReferralId(data.referralCode);
    if (!referrer) {
      throw new Error('Invalid referral code');
    }
    referrerId = referrer.id;
  }

  // Hash password
  const passwordHash = await hashPassword(data.password);

  // Generate unique referral ID
  let referralId: string;
  let isUnique = false;
  do {
    referralId = generateReferralId();
    const existing = await userDb.findByReferralId(referralId);
    isUnique = !existing;
  } while (!isUnique);



  // Create user in PostgreSQL
  const user = await userDb.create({
    email: data.email,
    firstName: data.firstName,
    lastName: data.lastName,
    password: passwordHash,
    referralId,
  });

  // Create referral relationship if referrer exists
  if (referrerId) {
    const { placeUserByReferralType } = await import('./referral');

    // Determine referral type based on placementSide parameter
    let referralType: 'general' | 'left' | 'right' = 'general';
    if (data.placementSide === 'left') {
      referralType = 'left';
    } else if (data.placementSide === 'right') {
      referralType = 'right';
    }

    // Place user using the new unified placement function
    await placeUserByReferralType(referrerId, user.id, referralType);
  }

  return {
    id: user.id,
    email: user.email,
    referralId: user.referralId,
    kycStatus: user.kycStatus,
  };
};

// User login
export const loginUser = async (data: {
  email: string;
  password: string;
}) => {
  const user = await userDb.findByEmail(data.email);
  if (!user) {
    throw new Error('Invalid email or password');
  }



  const isValidPassword = await verifyPassword(data.password, user.password);
  if (!isValidPassword) {
    throw new Error('Invalid email or password');
  }

  const token = generateToken({
    userId: user.id,
    email: user.email,
  });

  return {
    token,
    user: {
      id: user.id,
      email: user.email,
      referralId: user.referralId,
      kycStatus: user.kycStatus,
    },
  };
};

// Password validation
export const validatePassword = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

// Email validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Session management
export const createSession = (userId: string, email: string) => {
  return generateToken({ userId, email });
};

export const validateSession = (token: string) => {
  return verifyToken(token);
};

// Admin authentication
export const isAdmin = async (userId: string): Promise<boolean> => {
  const user = await userDb.findById(userId);
  return user?.role === 'ADMIN';
};
