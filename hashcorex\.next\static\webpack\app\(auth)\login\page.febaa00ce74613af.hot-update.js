"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/app/(auth)/login/page.tsx":
/*!***************************************!*\
  !*** ./src/app/(auth)/login/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons */ \"(app-pages-browser)/./src/components/icons/index.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,EyeOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,EyeOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,EyeOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/AuthGuard */ \"(app-pages-browser)/./src/components/auth/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// import { Container, Flex } from '@/components/layout';\n\n\n\n\nfunction LoginForm() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const { login, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: ''\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        try {\n            await login(formData.email, formData.password);\n            // Check for redirect parameter\n            const redirectTo = searchParams.get('redirect');\n            if (redirectTo && redirectTo.startsWith('/')) {\n                router.push(redirectTo);\n            } else {\n                router.push('/dashboard');\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Login failed');\n        }\n    };\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 animated-gradient opacity-30\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-white/95 via-solar-50/90 to-eco-50/95\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-10 w-16 h-16 bg-solar-400/20 rounded-full animate-float\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-40 right-20 w-12 h-12 bg-eco-400/20 rounded-full animate-float\",\n                style: {\n                    animationDelay: '2s'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-40 left-20 w-10 h-10 bg-purple-400/20 rounded-full animate-float\",\n                style: {\n                    animationDelay: '4s'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 min-h-screen flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-solar-500/10 to-eco-500/10 items-center justify-center p-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-lg text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_5__.SolarPanel, {\n                                            className: \"h-24 w-24 text-solar-500 mx-auto mb-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl xl:text-5xl font-black text-dark-900 mb-4\",\n                                            children: \"Welcome to the Future of Mining\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 leading-relaxed\",\n                                            children: \"Join thousands of users earning daily returns through our sustainable, solar-powered cryptocurrency mining platform.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 gap-6 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-eco-500/20 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-eco-600 font-bold\",\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-dark-900\",\n                                                            children: \"Eco-Friendly Mining\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"100% solar-powered operations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-solar-500/20 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-solar-600 font-bold\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-dark-900\",\n                                                            children: \"Daily Returns\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Consistent 0.6% - 1.1% daily ROI\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-600 font-bold\",\n                                                        children: \"\\uD83D\\uDD12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-dark-900\",\n                                                            children: \"Secure Platform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Bank-level security & encryption\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-4 lg:p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass-morphism rounded-3xl p-8 lg:p-10 shadow-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-8 lg:mb-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"inline-flex items-center space-x-3 mb-6 lg:mb-8 group lg:hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_5__.SolarPanel, {\n                                                                className: \"h-10 w-10 lg:h-12 lg:w-12 text-solar-500 group-hover:scale-110 transition-transform\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-solar-500/20 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl lg:text-4xl font-black bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent\",\n                                                        children: \"HashCoreX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl lg:text-3xl xl:text-4xl font-black text-dark-900 mb-3 lg:mb-4\",\n                                                children: \"Welcome Back\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base lg:text-lg text-gray-600 font-medium\",\n                                                children: \"Sign in to your mining dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                label: \"Email Address\",\n                                                type: \"email\",\n                                                name: \"email\",\n                                                value: formData.email,\n                                                onChange: handleChange,\n                                                placeholder: \"Enter your email\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                label: \"Password\",\n                                                type: showPassword ? 'text' : 'password',\n                                                name: \"password\",\n                                                value: formData.password,\n                                                onChange: handleChange,\n                                                placeholder: \"Enter your password\",\n                                                required: true,\n                                                rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"text-gray-400 hover:text-gray-600\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 35\n                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 68\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                className: \"rounded border-gray-300 text-solar-500 focus:ring-solar-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-600\",\n                                                                children: \"Remember me\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/forgot-password\",\n                                                        className: \"text-sm text-solar-500 hover:text-solar-600\",\n                                                        children: \"Forgot password?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"submit\",\n                                                variant: \"primary\",\n                                                size: \"xl\",\n                                                className: \"w-full font-bold\",\n                                                loading: loading,\n                                                children: \"Sign In to Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"Don't have an account?\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/register\",\n                                                    className: \"text-solar-500 hover:text-solar-600 font-medium\",\n                                                    children: \"Sign up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 text-center lg:hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Home\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginForm, \"Lioj1/aRG94UG8PnScYV12aZLIg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = LoginForm;\nfunction LoginPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthGuard__WEBPACK_IMPORTED_MODULE_7__.GuestRoute, {\n        redirectTo: \"/dashboard\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoginForm, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoginPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"LoginForm\");\n$RefreshReg$(_c1, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(auth)/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/AuthGuard.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/AuthGuard.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   GuestRoute: () => (/* binding */ GuestRoute),\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute),\n/* harmony export */   withAuthGuard: () => (/* binding */ withAuthGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,withAuthGuard,ProtectedRoute,GuestRoute auto */ var _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst AuthGuard = (param)=>{\n    let { children, requireAuth = false, requireGuest = false, redirectTo } = param;\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            if (loading) return; // Wait for auth check to complete\n            // If authentication is required but user is not logged in\n            if (requireAuth && !user) {\n                const loginUrl = \"/login?redirect=\".concat(encodeURIComponent(pathname));\n                router.replace(loginUrl);\n                return;\n            }\n            // If guest access is required but user is logged in\n            if (requireGuest && user) {\n                const destination = redirectTo || '/dashboard';\n                router.replace(destination);\n                return;\n            }\n        }\n    }[\"AuthGuard.useEffect\"], [\n        user,\n        loading,\n        requireAuth,\n        requireGuest,\n        router,\n        pathname,\n        redirectTo\n    ]);\n    // Show loading while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Loading, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Checking authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render children if auth requirements are not met\n    if (requireAuth && !user) {\n        return null; // Will redirect in useEffect\n    }\n    if (requireGuest && user) {\n        return null; // Will redirect in useEffect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_s(AuthGuard, \"+V/1yikrC2yNn6BpNR6HilodE6g=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AuthGuard;\n// Higher-order component for protecting pages\nconst withAuthGuard = function(Component) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n            lineNumber: 73,\n            columnNumber: 5\n        }, _this);\n    WrappedComponent.displayName = \"withAuthGuard(\".concat(Component.displayName || Component.name, \")\");\n    return WrappedComponent;\n};\n// Convenience components\nconst ProtectedRoute = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requireAuth: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = ProtectedRoute;\nconst GuestRoute = (param)=>{\n    let { children, redirectTo } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requireGuest: true,\n        redirectTo: redirectTo,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\auth\\\\AuthGuard.tsx\",\n        lineNumber: 92,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = GuestRoute;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AuthGuard\");\n$RefreshReg$(_c1, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"GuestRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2F1dGgvQXV0aEd1YXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXlDO0FBQ2dCO0FBQ2Y7QUFDQTtBQVNuQyxNQUFNTSxZQUFzQztRQUFDLEVBQ2xEQyxRQUFRLEVBQ1JDLGNBQWMsS0FBSyxFQUNuQkMsZUFBZSxLQUFLLEVBQ3BCQyxVQUFVLEVBQ1g7O0lBQ0MsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHUix1REFBT0E7SUFDakMsTUFBTVMsU0FBU1gsMERBQVNBO0lBQ3hCLE1BQU1ZLFdBQVdYLDREQUFXQTtJQUU1QkYsZ0RBQVNBOytCQUFDO1lBQ1IsSUFBSVcsU0FBUyxRQUFRLGtDQUFrQztZQUV2RCwwREFBMEQ7WUFDMUQsSUFBSUosZUFBZSxDQUFDRyxNQUFNO2dCQUN4QixNQUFNSSxXQUFXLG1CQUFnRCxPQUE3QkMsbUJBQW1CRjtnQkFDdkRELE9BQU9JLE9BQU8sQ0FBQ0Y7Z0JBQ2Y7WUFDRjtZQUVBLG9EQUFvRDtZQUNwRCxJQUFJTixnQkFBZ0JFLE1BQU07Z0JBQ3hCLE1BQU1PLGNBQWNSLGNBQWM7Z0JBQ2xDRyxPQUFPSSxPQUFPLENBQUNDO2dCQUNmO1lBQ0Y7UUFDRjs4QkFBRztRQUFDUDtRQUFNQztRQUFTSjtRQUFhQztRQUFjSTtRQUFRQztRQUFVSjtLQUFXO0lBRTNFLDZDQUE2QztJQUM3QyxJQUFJRSxTQUFTO1FBQ1gscUJBQ0UsOERBQUNPO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ2YsbURBQU9BOzs7OztrQ0FDUiw4REFBQ2dCO3dCQUFFRCxXQUFVO2tDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJMUM7SUFFQSx5REFBeUQ7SUFDekQsSUFBSVosZUFBZSxDQUFDRyxNQUFNO1FBQ3hCLE9BQU8sTUFBTSw2QkFBNkI7SUFDNUM7SUFFQSxJQUFJRixnQkFBZ0JFLE1BQU07UUFDeEIsT0FBTyxNQUFNLDZCQUE2QjtJQUM1QztJQUVBLHFCQUFPO2tCQUFHSjs7QUFDWixFQUFFO0dBbERXRDs7UUFNZUYsbURBQU9BO1FBQ2xCRixzREFBU0E7UUFDUEMsd0RBQVdBOzs7S0FSakJHO0FBb0RiLDhDQUE4QztBQUN2QyxNQUFNZ0IsZ0JBQWdCLFNBQzNCQztRQUNBQywyRUFBNEMsQ0FBQztJQUU3QyxNQUFNQyxtQkFBbUIsQ0FBQ0Msc0JBQ3hCLDhEQUFDcEI7WUFBVyxHQUFHa0IsT0FBTztzQkFDcEIsNEVBQUNEO2dCQUFXLEdBQUdHLEtBQUs7Ozs7Ozs7Ozs7O0lBSXhCRCxpQkFBaUJFLFdBQVcsR0FBRyxpQkFBeUQsT0FBeENKLFVBQVVJLFdBQVcsSUFBSUosVUFBVUssSUFBSSxFQUFDO0lBRXhGLE9BQU9IO0FBQ1QsRUFBRTtBQUVGLHlCQUF5QjtBQUNsQixNQUFNSSxpQkFBMEQ7UUFBQyxFQUFFdEIsUUFBUSxFQUFFO3lCQUNsRiw4REFBQ0Q7UUFBVUUsV0FBVztrQkFBRUQ7Ozs7OztFQUN4QjtNQUZXc0I7QUFJTixNQUFNQyxhQUEyRTtRQUFDLEVBQ3ZGdkIsUUFBUSxFQUNSRyxVQUFVLEVBQ1g7eUJBQ0MsOERBQUNKO1FBQVVHLFlBQVk7UUFBQ0MsWUFBWUE7a0JBQWFIOzs7Ozs7RUFDakQ7TUFMV3VCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRyZWFtXFxEZXNrdG9wXFxIYXNoX01pbmluZ3NcXGhhc2hjb3JleFxcc3JjXFxjb21wb25lbnRzXFxhdXRoXFxBdXRoR3VhcmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvaG9va3MvdXNlQXV0aCc7XG5pbXBvcnQgeyBMb2FkaW5nIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpJztcblxuaW50ZXJmYWNlIEF1dGhHdWFyZFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgcmVxdWlyZUF1dGg/OiBib29sZWFuO1xuICByZXF1aXJlR3Vlc3Q/OiBib29sZWFuO1xuICByZWRpcmVjdFRvPzogc3RyaW5nO1xufVxuXG5leHBvcnQgY29uc3QgQXV0aEd1YXJkOiBSZWFjdC5GQzxBdXRoR3VhcmRQcm9wcz4gPSAoe1xuICBjaGlsZHJlbixcbiAgcmVxdWlyZUF1dGggPSBmYWxzZSxcbiAgcmVxdWlyZUd1ZXN0ID0gZmFsc2UsXG4gIHJlZGlyZWN0VG8sXG59KSA9PiB7XG4gIGNvbnN0IHsgdXNlciwgbG9hZGluZyB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGxvYWRpbmcpIHJldHVybjsgLy8gV2FpdCBmb3IgYXV0aCBjaGVjayB0byBjb21wbGV0ZVxuXG4gICAgLy8gSWYgYXV0aGVudGljYXRpb24gaXMgcmVxdWlyZWQgYnV0IHVzZXIgaXMgbm90IGxvZ2dlZCBpblxuICAgIGlmIChyZXF1aXJlQXV0aCAmJiAhdXNlcikge1xuICAgICAgY29uc3QgbG9naW5VcmwgPSBgL2xvZ2luP3JlZGlyZWN0PSR7ZW5jb2RlVVJJQ29tcG9uZW50KHBhdGhuYW1lKX1gO1xuICAgICAgcm91dGVyLnJlcGxhY2UobG9naW5VcmwpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIElmIGd1ZXN0IGFjY2VzcyBpcyByZXF1aXJlZCBidXQgdXNlciBpcyBsb2dnZWQgaW5cbiAgICBpZiAocmVxdWlyZUd1ZXN0ICYmIHVzZXIpIHtcbiAgICAgIGNvbnN0IGRlc3RpbmF0aW9uID0gcmVkaXJlY3RUbyB8fCAnL2Rhc2hib2FyZCc7XG4gICAgICByb3V0ZXIucmVwbGFjZShkZXN0aW5hdGlvbik7XG4gICAgICByZXR1cm47XG4gICAgfVxuICB9LCBbdXNlciwgbG9hZGluZywgcmVxdWlyZUF1dGgsIHJlcXVpcmVHdWVzdCwgcm91dGVyLCBwYXRobmFtZSwgcmVkaXJlY3RUb10pO1xuXG4gIC8vIFNob3cgbG9hZGluZyB3aGlsZSBjaGVja2luZyBhdXRoZW50aWNhdGlvblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmF5LTUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8TG9hZGluZyAvPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1ncmF5LTYwMFwiPkNoZWNraW5nIGF1dGhlbnRpY2F0aW9uLi4uPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBEb24ndCByZW5kZXIgY2hpbGRyZW4gaWYgYXV0aCByZXF1aXJlbWVudHMgYXJlIG5vdCBtZXRcbiAgaWYgKHJlcXVpcmVBdXRoICYmICF1c2VyKSB7XG4gICAgcmV0dXJuIG51bGw7IC8vIFdpbGwgcmVkaXJlY3QgaW4gdXNlRWZmZWN0XG4gIH1cblxuICBpZiAocmVxdWlyZUd1ZXN0ICYmIHVzZXIpIHtcbiAgICByZXR1cm4gbnVsbDsgLy8gV2lsbCByZWRpcmVjdCBpbiB1c2VFZmZlY3RcbiAgfVxuXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz47XG59O1xuXG4vLyBIaWdoZXItb3JkZXIgY29tcG9uZW50IGZvciBwcm90ZWN0aW5nIHBhZ2VzXG5leHBvcnQgY29uc3Qgd2l0aEF1dGhHdWFyZCA9IChcbiAgQ29tcG9uZW50OiBSZWFjdC5Db21wb25lbnRUeXBlPGFueT4sXG4gIG9wdGlvbnM6IE9taXQ8QXV0aEd1YXJkUHJvcHMsICdjaGlsZHJlbic+ID0ge31cbikgPT4ge1xuICBjb25zdCBXcmFwcGVkQ29tcG9uZW50ID0gKHByb3BzOiBhbnkpID0+IChcbiAgICA8QXV0aEd1YXJkIHsuLi5vcHRpb25zfT5cbiAgICAgIDxDb21wb25lbnQgey4uLnByb3BzfSAvPlxuICAgIDwvQXV0aEd1YXJkPlxuICApO1xuXG4gIFdyYXBwZWRDb21wb25lbnQuZGlzcGxheU5hbWUgPSBgd2l0aEF1dGhHdWFyZCgke0NvbXBvbmVudC5kaXNwbGF5TmFtZSB8fCBDb21wb25lbnQubmFtZX0pYDtcbiAgXG4gIHJldHVybiBXcmFwcGVkQ29tcG9uZW50O1xufTtcblxuLy8gQ29udmVuaWVuY2UgY29tcG9uZW50c1xuZXhwb3J0IGNvbnN0IFByb3RlY3RlZFJvdXRlOiBSZWFjdC5GQzx7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfT4gPSAoeyBjaGlsZHJlbiB9KSA9PiAoXG4gIDxBdXRoR3VhcmQgcmVxdWlyZUF1dGg+e2NoaWxkcmVufTwvQXV0aEd1YXJkPlxuKTtcblxuZXhwb3J0IGNvbnN0IEd1ZXN0Um91dGU6IFJlYWN0LkZDPHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTsgcmVkaXJlY3RUbz86IHN0cmluZyB9PiA9ICh7IFxuICBjaGlsZHJlbiwgXG4gIHJlZGlyZWN0VG8gXG59KSA9PiAoXG4gIDxBdXRoR3VhcmQgcmVxdWlyZUd1ZXN0IHJlZGlyZWN0VG89e3JlZGlyZWN0VG99PntjaGlsZHJlbn08L0F1dGhHdWFyZD5cbik7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VQYXRobmFtZSIsInVzZUF1dGgiLCJMb2FkaW5nIiwiQXV0aEd1YXJkIiwiY2hpbGRyZW4iLCJyZXF1aXJlQXV0aCIsInJlcXVpcmVHdWVzdCIsInJlZGlyZWN0VG8iLCJ1c2VyIiwibG9hZGluZyIsInJvdXRlciIsInBhdGhuYW1lIiwibG9naW5VcmwiLCJlbmNvZGVVUklDb21wb25lbnQiLCJyZXBsYWNlIiwiZGVzdGluYXRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwid2l0aEF1dGhHdWFyZCIsIkNvbXBvbmVudCIsIm9wdGlvbnMiLCJXcmFwcGVkQ29tcG9uZW50IiwicHJvcHMiLCJkaXNwbGF5TmFtZSIsIm5hbWUiLCJQcm90ZWN0ZWRSb3V0ZSIsIkd1ZXN0Um91dGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/AuthGuard.tsx\n"));

/***/ })

});