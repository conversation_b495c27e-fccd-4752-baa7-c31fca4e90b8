'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button, Input } from '@/components/ui';
import { SolarPanel } from '@/components/icons';
import { ArrowLeft, Mail, Eye, EyeOff, Check } from 'lucide-react';
import { validatePassword } from '@/lib/utils';
import { GuestRoute } from '@/components/auth/AuthGuard';
import { OTPVerification } from '@/components/auth/OTPVerification';

function ForgotPasswordForm() {
  const router = useRouter();
  const [step, setStep] = useState<'email' | 'otp' | 'password'>('email');
  const [email, setEmail] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const passwordValidation = validatePassword(newPassword);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          purpose: 'password_reset',
        }),
      });

      const data = await response.json();

      if (data.success) {
        setStep('otp');
      } else {
        setError(data.error || 'Failed to send reset code');
      }
    } catch (error) {
      setError('Failed to send reset code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOTPVerified = async (otp: string) => {
    setError('');
    setLoading(true);

    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          otp,
          purpose: 'password_reset',
        }),
      });

      const data = await response.json();

      if (data.success) {
        setStep('password');
      } else {
        setError(data.error || 'Invalid OTP');
      }
    } catch (error) {
      setError('Failed to verify OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setError('');
    setLoading(true);

    try {
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          purpose: 'password_reset',
        }),
      });

      const data = await response.json();

      if (!data.success) {
        setError(data.error || 'Failed to resend code');
      }
    } catch (error) {
      setError('Failed to resend code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (!passwordValidation.isValid) {
      setError('Please ensure your password meets all requirements');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          newPassword,
          confirmPassword,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Password reset successful! Redirecting to dashboard...');
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        setError(data.error || 'Failed to reset password');
      }
    } catch (error) {
      setError('Failed to reset password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex">
      {/* Left Side - Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-6">
                <SolarPanel className="h-10 w-10 text-solar-500" />
                <span className="ml-3 text-2xl font-bold text-gray-900">HashCoreX</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {step === 'email' && 'Reset Password'}
                {step === 'otp' && 'Verify Email'}
                {step === 'password' && 'Set New Password'}
              </h1>
              <p className="text-gray-600">
                {step === 'email' && 'Enter your email to receive a reset code'}
                {step === 'otp' && 'Enter the verification code sent to your email'}
                {step === 'password' && 'Create a new secure password'}
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* Success Message */}
            {success && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-600 text-sm">{success}</p>
              </div>
            )}

            {/* Email Step */}
            {step === 'email' && (
              <form onSubmit={handleEmailSubmit} className="space-y-6">
                <Input
                  label="Email Address"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                />

                <Button
                  type="submit"
                  variant="primary"
                  size="xl"
                  className="w-full font-bold"
                  loading={loading}
                >
                  {loading ? 'Sending Code...' : 'Send Reset Code'}
                </Button>
              </form>
            )}

            {/* OTP Step */}
            {step === 'otp' && (
              <div className="space-y-6">
                <OTPVerification
                  email={email}
                  onVerified={handleOTPVerified}
                  onResend={handleResendOTP}
                  loading={loading}
                  error={error}
                />
              </div>
            )}

            {/* Password Step */}
            {step === 'password' && (
              <form onSubmit={handlePasswordReset} className="space-y-6">
                <Input
                  label="New Password"
                  type={showPassword ? 'text' : 'password'}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="Enter new password"
                  required
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  }
                />

                <Input
                  label="Confirm New Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm new password"
                  required
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  }
                />

                {/* Password Requirements */}
                {newPassword && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">Password Requirements:</p>
                    <div className="space-y-1">
                      {passwordValidation.checks.map((check, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Check className={`h-4 w-4 ${check.valid ? 'text-green-500' : 'text-gray-300'}`} />
                          <span className={`text-sm ${check.valid ? 'text-green-600' : 'text-gray-500'}`}>
                            {check.message}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Button
                  type="submit"
                  variant="primary"
                  size="xl"
                  className="w-full font-bold"
                  loading={loading}
                  disabled={!passwordValidation.isValid || newPassword !== confirmPassword}
                >
                  {loading ? 'Resetting Password...' : 'Reset Password'}
                </Button>
              </form>
            )}

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-gray-600">
                Remember your password?{' '}
                <Link href="/login" className="text-solar-500 hover:text-solar-600 font-medium">
                  Sign in
                </Link>
              </p>
            </div>

            {/* Back to Home */}
            <div className="mt-6 text-center lg:hidden">
              <Link href="/" className="inline-flex items-center text-gray-500 hover:text-gray-700 text-sm">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Image */}
      <div className="hidden lg:block lg:flex-1 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-solar-600 to-blue-600">
          <div className="absolute inset-0 bg-black bg-opacity-20"></div>
          <div className="relative h-full flex items-center justify-center p-12">
            <div className="text-center text-white">
              <h2 className="text-4xl font-bold mb-6">Secure Password Reset</h2>
              <p className="text-xl text-blue-100 mb-8">
                Reset your password securely with email verification
              </p>
              <div className="grid grid-cols-1 gap-6 max-w-sm mx-auto">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <Mail className="h-6 w-6" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold">Email Verification</h3>
                    <p className="text-sm text-blue-100">Secure OTP verification</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ForgotPasswordPage() {
  return (
    <GuestRoute redirectTo="/dashboard">
      <ForgotPasswordForm />
    </GuestRoute>
  );
}
