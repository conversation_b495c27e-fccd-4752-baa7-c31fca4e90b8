"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardOverview */ \"(app-pages-browser)/./src/components/dashboard/DashboardOverview.tsx\");\n/* harmony import */ var _components_dashboard_PurchaseMiningUnit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/PurchaseMiningUnit */ \"(app-pages-browser)/./src/components/dashboard/PurchaseMiningUnit.tsx\");\n/* harmony import */ var _components_dashboard_EarningsTracker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/EarningsTracker */ \"(app-pages-browser)/./src/components/dashboard/EarningsTracker.tsx\");\n/* harmony import */ var _components_dashboard_WalletDashboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/WalletDashboard */ \"(app-pages-browser)/./src/components/dashboard/WalletDashboard.tsx\");\n/* harmony import */ var _components_dashboard_D3BinaryTree__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/D3BinaryTree */ \"(app-pages-browser)/./src/components/dashboard/D3BinaryTree.tsx\");\n/* harmony import */ var _components_dashboard_KYCPortal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/KYCPortal */ \"(app-pages-browser)/./src/components/dashboard/KYCPortal.tsx\");\n/* harmony import */ var _components_dashboard_MiningUnitsTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/MiningUnitsTable */ \"(app-pages-browser)/./src/components/dashboard/MiningUnitsTable.tsx\");\n/* harmony import */ var _components_dashboard_SupportCenter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/SupportCenter */ \"(app-pages-browser)/./src/components/dashboard/SupportCenter.tsx\");\n/* harmony import */ var _components_dashboard_UserProfileSettings__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/UserProfileSettings */ \"(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__  auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardContent() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'overview':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_3__.DashboardOverview, {\n                    onTabChange: setActiveTab\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 16\n                }, this);\n            case 'mining':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_PurchaseMiningUnit__WEBPACK_IMPORTED_MODULE_4__.PurchaseMiningUnit, {\n                            onPurchaseComplete: ()=>window.location.reload()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MiningUnitsTable__WEBPACK_IMPORTED_MODULE_9__.MiningUnitsTable, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this);\n            case 'earnings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_EarningsTracker__WEBPACK_IMPORTED_MODULE_5__.EarningsTracker, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, this);\n            case 'wallet':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_WalletDashboard__WEBPACK_IMPORTED_MODULE_6__.WalletDashboard, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 16\n                }, this);\n            case 'referrals':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_D3BinaryTree__WEBPACK_IMPORTED_MODULE_7__.D3BinaryTree, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, this);\n            case 'kyc':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_KYCPortal__WEBPACK_IMPORTED_MODULE_8__.KYCPortal, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 16\n                }, this);\n            case 'support':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SupportCenter__WEBPACK_IMPORTED_MODULE_10__.SupportCenter, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case 'profile':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_UserProfileSettings__WEBPACK_IMPORTED_MODULE_11__.UserProfileSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardOverview__WEBPACK_IMPORTED_MODULE_3__.DashboardOverview, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__.DashboardLayout, {\n        activeTab: activeTab,\n        onTabChange: setActiveTab,\n        children: renderTabContent()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardContent, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = DashboardContent;\nvar _c;\n$RefreshReg$(_c, \"DashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});