import { NextRequest, NextResponse } from 'next/server';
import { otpDb, userDb, emailLogDb } from '@/lib/database';
import { emailService, generateOTP } from '@/lib/email';
import { ErrorLogger } from '@/lib/errorLogger';

// POST - Send OTP for email verification
export async function POST(request: NextRequest) {
  try {
    const { email, firstName } = await request.json();

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if email is already registered
    const existingUser = await userDb.findByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'Email is already registered' },
        { status: 400 }
      );
    }

    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10); // 10 minutes expiry

    // Save OTP to database
    await otpDb.create({
      email,
      otp,
      purpose: 'email_verification',
      expiresAt,
    });

    // Create email log entry
    const emailLog = await emailLogDb.create({
      to: email,
      subject: 'Email Verification - HashCoreX',
      template: 'otp_verification',
      status: 'PENDING',
    });

    // Send OTP email
    try {
      const emailSent = await emailService.sendOTPEmail(email, otp, firstName);
      
      if (emailSent) {
        await emailLogDb.updateStatus(emailLog.id, 'SENT');
        
        return NextResponse.json({
          success: true,
          message: 'OTP sent successfully to your email',
          data: {
            email,
            expiresAt: expiresAt.toISOString(),
          },
        });
      } else {
        await emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');
        
        return NextResponse.json(
          { success: false, error: 'Failed to send OTP email. Please try again.' },
          { status: 500 }
        );
      }
    } catch (emailError) {
      await emailLogDb.updateStatus(emailLog.id, 'FAILED', emailError instanceof Error ? emailError.message : 'Unknown error');
      
      console.error('Email sending error:', emailError);
      return NextResponse.json(
        { success: false, error: 'Failed to send OTP email. Please check your email configuration.' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Send OTP error:', error);
    
    await ErrorLogger.logApiError(
      request,
      error as Error,
      'SEND_OTP_ERROR'
    );

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
