import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from '@/lib/auth';

// Define protected and auth routes
const protectedRoutes = ['/dashboard', '/admin'];
const authRoutes = ['/login', '/register'];
const publicRoutes = ['/', '/about', '/contact'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log('Middleware - pathname:', pathname);

  // Get token from cookie
  const token = request.cookies.get('auth-token')?.value;
  console.log('Middleware - token exists:', !!token);

  // Verify token
  let isAuthenticated = false;
  let user = null;

  if (token) {
    try {
      const decoded = verifyToken(token);
      console.log('Middleware - token decoded:', !!decoded);
      if (decoded) {
        isAuthenticated = true;
        user = decoded;
      }
    } catch (error) {
      console.log('Middleware - token verification failed:', error);
      // Token is invalid, remove it
      const response = NextResponse.next();
      response.cookies.delete('auth-token');
      return response;
    }
  }

  console.log('Middleware - isAuthenticated:', isAuthenticated);

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // Check if the current path is an auth route (login/register)
  const isAuthRoute = authRoutes.some(route => 
    pathname.startsWith(route)
  );

  // If user is not authenticated and trying to access protected route
  if (!isAuthenticated && isProtectedRoute) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If user is authenticated and trying to access auth routes
  if (isAuthenticated && isAuthRoute) {
    // Check if there's a redirect parameter
    const redirectUrl = request.nextUrl.searchParams.get('redirect');
    if (redirectUrl && redirectUrl.startsWith('/')) {
      return NextResponse.redirect(new URL(redirectUrl, request.url));
    }
    // Default redirect to dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // For all other routes, continue normally
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     * - dashboard (temporarily disabled for debugging)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|uploads|crypto-icons|dashboard).*)',
  ],
};
