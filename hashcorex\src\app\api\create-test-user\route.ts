import { NextRequest, NextResponse } from 'next/server';
import { hashPassword } from '@/lib/auth';
import { userDb } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { success: false, error: 'Only available in development' },
        { status: 403 }
      );
    }

    const testEmail = '<EMAIL>';
    const testPassword = 'Test123!';
    
    // Check if user already exists
    const existingUser = await userDb.findByEmail(testEmail);
    if (existingUser) {
      return NextResponse.json({
        success: true,
        message: 'Test user already exists',
        data: {
          email: testEmail,
          password: testPassword,
        },
      });
    }

    // Create test user
    const hashedPassword = await hashPassword(testPassword);
    
    const user = await userDb.create({
      email: testEmail,
      firstName: 'Test',
      lastName: 'User',
      password: hashedPassword,
      referralId: 'TEST123',
      isActive: true,
      kycStatus: 'APPROVED',
    });

    return NextResponse.json({
      success: true,
      message: 'Test user created successfully',
      data: {
        email: testEmail,
        password: testPassword,
        userId: user.id,
      },
    });

  } catch (error: any) {
    console.error('Create test user error:', error);
    
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}
