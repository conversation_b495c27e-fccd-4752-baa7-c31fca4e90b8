/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/deposits/route";
exports.ids = ["app/api/admin/deposits/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fdeposits%2Froute&page=%2Fapi%2Fadmin%2Fdeposits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdeposits%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fdeposits%2Froute&page=%2Fapi%2Fadmin%2Fdeposits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdeposits%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_admin_deposits_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/deposits/route.ts */ \"(rsc)/./src/app/api/admin/deposits/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/deposits/route\",\n        pathname: \"/api/admin/deposits\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/deposits/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\admin\\\\deposits\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_admin_deposits_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fdeposits%2Froute&page=%2Fapi%2Fadmin%2Fdeposits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdeposits%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/deposits/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/admin/deposits/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\n// GET - Fetch all deposits for admin\nasync function GET(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        const userIsAdmin = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.isAdmin)(user.id);\n        if (!userIsAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Admin access required'\n            }, {\n                status: 403\n            });\n        }\n        // Parse query parameters\n        const { searchParams } = new URL(request.url);\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const offset = parseInt(searchParams.get('offset') || '0');\n        const status = searchParams.get('status');\n        const userId = searchParams.get('userId') || undefined;\n        // Fetch deposits with filters\n        let deposits;\n        if (userId) {\n            deposits = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.depositTransactionDb.findByUserId(userId, {\n                status: status || undefined,\n                limit: Math.min(limit, 100),\n                offset\n            });\n        } else {\n            deposits = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.depositTransactionDb.findAll({\n                status: status || undefined,\n                limit: Math.min(limit, 100),\n                offset\n            });\n        }\n        // Get deposit statistics\n        const stats = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.depositTransactionDb.getDepositStats();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                deposits: deposits.map((deposit)=>({\n                        id: deposit.id,\n                        userId: deposit.userId,\n                        user: deposit.user ? {\n                            id: deposit.user.id,\n                            email: deposit.user.email,\n                            firstName: deposit.user.firstName,\n                            lastName: deposit.user.lastName\n                        } : null,\n                        transactionId: deposit.transactionId,\n                        amount: deposit.amount,\n                        usdtAmount: deposit.usdtAmount,\n                        tronAddress: deposit.tronAddress,\n                        senderAddress: deposit.senderAddress,\n                        status: deposit.status,\n                        blockNumber: deposit.blockNumber,\n                        blockTimestamp: deposit.blockTimestamp,\n                        confirmations: deposit.confirmations,\n                        verifiedAt: deposit.verifiedAt,\n                        processedAt: deposit.processedAt,\n                        failureReason: deposit.failureReason,\n                        createdAt: deposit.createdAt,\n                        updatedAt: deposit.updatedAt\n                    })),\n                stats: {\n                    totalDeposits: stats.totalDeposits,\n                    totalAmount: stats.totalAmount,\n                    pendingDeposits: stats.pendingDeposits\n                },\n                pagination: {\n                    limit,\n                    offset,\n                    hasMore: deposits.length === limit\n                },\n                filters: {\n                    status,\n                    userId\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Admin deposits fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch deposits'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Admin actions on deposits (approve, reject, etc.)\nasync function POST(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        const userIsAdmin = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.isAdmin)(user.id);\n        if (!userIsAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Admin access required'\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { action, transactionId, reason } = body;\n        if (!action || !transactionId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Action and transaction ID are required'\n            }, {\n                status: 400\n            });\n        }\n        // Find the deposit\n        const deposit = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.depositTransactionDb.findByTransactionId(transactionId);\n        if (!deposit) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Deposit not found'\n            }, {\n                status: 404\n            });\n        }\n        // Manual actions are no longer supported - deposits are processed automatically\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Manual deposit actions are no longer supported. Deposits are processed automatically by the system.'\n        }, {\n            status: 400\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Deposit ${action}ed successfully`,\n            data: result\n        });\n    } catch (error) {\n        console.error('Admin deposit action error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to process deposit action'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/deposits/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, 12);\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fdeposits%2Froute&page=%2Fapi%2Fadmin%2Fdeposits%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdeposits%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();