"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(middleware)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(middleware)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(middleware)/./src/lib/database.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30d';\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, 12);\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    console.log('Generating token with payload:', payload);\n    console.log('Using JWT_SECRET length:', JWT_SECRET.length);\n    const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n    console.log('Generated token length:', token.length);\n    return token;\n};\nconst verifyToken = (token)=>{\n    try {\n        console.log('Verifying token with secret length:', JWT_SECRET.length);\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        console.log('Token verified successfully:', {\n            userId: decoded.userId,\n            email: decoded.email\n        });\n        return decoded;\n    } catch (error) {\n        console.log('Token verification failed:', error);\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(middleware)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/auth.ts\n");

/***/ })

});