"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/UserProfileSettings.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProfileSettings: () => (/* binding */ UserProfileSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* __next_internal_client_entry_do_not_use__ UserProfileSettings auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst UserProfileSettings = ()=>{\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: true,\n        smsNotifications: false,\n        marketingEmails: false\n    });\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        referralId: ''\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordForm, setPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [withdrawalAddress, setWithdrawalAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadingPicture, setUploadingPicture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Message box hook\n    const { showMessage, MessageBoxComponent } = (0,_components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserProfileSettings.useEffect\": ()=>{\n            if (user) {\n                setProfileData({\n                    firstName: user.firstName || '',\n                    lastName: user.lastName || '',\n                    email: user.email || '',\n                    referralId: user.referralId || ''\n                });\n            }\n            fetchNotificationSettings();\n            fetchUserData();\n        }\n    }[\"UserProfileSettings.useEffect\"], [\n        user\n    ]);\n    const fetchNotificationSettings = async ()=>{\n        try {\n            const response = await fetch('/api/user/notification-settings', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setNotifications(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch notification settings:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch('/api/user/withdrawal-address', {\n                credentials: 'include'\n            });\n            const data = await response.json();\n            if (data.success) {\n                setWithdrawalAddress(data.data.withdrawalAddress || '');\n            }\n        } catch (error) {\n            console.error('Failed to fetch user data:', error);\n        }\n    };\n    const updateNotificationSettings = async (settings)=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/user/notification-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(settings)\n            });\n            if (response.ok) {\n                setNotifications(settings);\n            }\n        } catch (error) {\n            console.error('Failed to update notification settings:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const updateProfile = async ()=>{\n        try {\n            setSaving(true);\n            // Profile update API call would go here\n            console.log('Profile update:', profileData);\n        } catch (error) {\n            console.error('Failed to update profile:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const updatePassword = async ()=>{\n        if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n            showMessage({\n                title: 'Password Mismatch',\n                message: 'New passwords do not match',\n                variant: 'error'\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            // Password update API call would go here\n            console.log('Password update');\n            setPasswordForm({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Failed to update password:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, undefined);\n    }\n    const tabs = [\n        {\n            id: 'profile',\n            label: 'Profile Information',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security Settings',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notification Settings',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'billing',\n            label: 'Billing & Payments',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ];\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'profile':\n                return renderProfileContent();\n            case 'security':\n                return renderSecurityContent();\n            case 'notifications':\n                return renderNotificationContent();\n            case 'billing':\n                return renderBillingContent();\n            default:\n                return renderProfileContent();\n        }\n    };\n    const renderProfileContent = ()=>{\n        const isKycSubmittedOrApproved = (user === null || user === void 0 ? void 0 : user.kycStatus) === 'PENDING' || (user === null || user === void 0 ? void 0 : user.kycStatus) === 'APPROVED';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Profile Information\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 font-medium\",\n                                            children: \"Profile names cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-1\",\n                                    children: \"Your identity has been verified and profile information is now locked for security.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"First Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"text\",\n                                            value: profileData.firstName,\n                                            onChange: (e)=>setProfileData((prev)=>({\n                                                        ...prev,\n                                                        firstName: e.target.value\n                                                    })),\n                                            disabled: isKycSubmittedOrApproved,\n                                            className: isKycSubmittedOrApproved ? \"bg-gray-100 border-gray-300 text-gray-500\" : \"bg-white border-gray-300 text-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Last Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"text\",\n                                            value: profileData.lastName,\n                                            onChange: (e)=>setProfileData((prev)=>({\n                                                        ...prev,\n                                                        lastName: e.target.value\n                                                    })),\n                                            disabled: isKycSubmittedOrApproved,\n                                            className: isKycSubmittedOrApproved ? \"bg-gray-100 border-gray-300 text-gray-500\" : \"bg-white border-gray-300 text-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"email\",\n                                    value: profileData.email,\n                                    disabled: true,\n                                    className: \"bg-gray-100 border-gray-300 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Email cannot be changed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Referral ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"text\",\n                                    value: profileData.referralId,\n                                    disabled: true,\n                                    className: \"bg-gray-100 border-gray-300 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Your unique referral identifier\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updateProfile,\n                            disabled: saving || isKycSubmittedOrApproved,\n                            className: \"w-full bg-yellow-500 hover:bg-yellow-600 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, undefined),\n                                saving ? 'Saving...' : isKycSubmittedOrApproved ? 'Profile Locked' : 'Save Profile'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderSecurityContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Security Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Current Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            value: passwordForm.currentPassword,\n                                            onChange: (e)=>setPasswordForm((prev)=>({\n                                                        ...prev,\n                                                        currentPassword: e.target.value\n                                                    })),\n                                            className: \"bg-white border-gray-300 text-gray-900 pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: passwordForm.newPassword,\n                                    onChange: (e)=>setPasswordForm((prev)=>({\n                                                ...prev,\n                                                newPassword: e.target.value\n                                            })),\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: passwordForm.confirmPassword,\n                                    onChange: (e)=>setPasswordForm((prev)=>({\n                                                ...prev,\n                                                confirmPassword: e.target.value\n                                            })),\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updatePassword,\n                            disabled: saving || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword,\n                            className: \"w-full bg-red-500 hover:bg-red-600 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 11\n                                }, undefined),\n                                saving ? 'Updating...' : 'Update Password'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 303,\n            columnNumber: 5\n        }, undefined);\n    const renderNotificationContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Notification Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Email Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Receive updates via email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.emailNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    emailNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Push Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Browser notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.pushNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    pushNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"SMS Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Text message alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.smsNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    smsNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 370,\n            columnNumber: 5\n        }, undefined);\n    const updateWithdrawalAddress = async ()=>{\n        try {\n            setSaving(true);\n            // Validate USDT TRC20 address format\n            if (withdrawalAddress && !withdrawalAddress.match(/^T[A-Za-z1-9]{33}$/)) {\n                showMessage({\n                    title: 'Invalid Address',\n                    message: 'Invalid USDT TRC20 address format. Address must start with \"T\" and be 34 characters long.',\n                    variant: 'error'\n                });\n                return;\n            }\n            const response = await fetch('/api/user/withdrawal-address', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    withdrawalAddress: withdrawalAddress.trim()\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Response not OK:', response.status, errorText);\n                throw new Error(\"Server error: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update withdrawal address');\n            }\n            // Show success message\n            showMessage({\n                title: 'Success',\n                message: 'Withdrawal address updated successfully!',\n                variant: 'success'\n            });\n        } catch (error) {\n            console.error('Failed to update withdrawal address:', error);\n            showMessage({\n                title: 'Error',\n                message: \"Error: \".concat(error.message),\n                variant: 'error'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const renderBillingContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Billing & Payments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Default Withdrawal Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"text\",\n                                    value: withdrawalAddress,\n                                    onChange: (e)=>setWithdrawalAddress(e.target.value),\n                                    placeholder: \"Enter your USDT TRC20 address\",\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"This address will be automatically filled in withdrawal forms. Only USDT TRC20 addresses are supported.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-800 mb-1\",\n                                                children: \"Security Notice\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: \"Your withdrawal address is encrypted and stored securely. You can update it anytime, but make sure to double-check the address as transactions cannot be reversed.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updateWithdrawalAddress,\n                            disabled: saving,\n                            className: \"w-full bg-yellow-500 hover:bg-yellow-600 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 11\n                                }, undefined),\n                                saving ? 'Saving...' : 'Save Withdrawal Address'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 504,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Profile & Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Manage your account information and preferences\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 556,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: tabs.map((tab)=>{\n                        const Icon = tab.icon;\n                        const isActive = activeTab === tab.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"\\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\\n                  \".concat(isActive ? 'border-yellow-500 text-yellow-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \"\\n                \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: renderTabContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 589,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBoxComponent, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 594,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n        lineNumber: 554,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UserProfileSettings, \"DGT74mfTk7N0X7+bUy0j1TqKodI=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox\n    ];\n});\n_c = UserProfileSettings;\nvar _c;\n$RefreshReg$(_c, \"UserProfileSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx\n"));

/***/ })

});