/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/referrals/tree/route";
exports.ids = ["app/api/referrals/tree/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freferrals%2Ftree%2Froute&page=%2Fapi%2Freferrals%2Ftree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freferrals%2Ftree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freferrals%2Ftree%2Froute&page=%2Fapi%2Freferrals%2Ftree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freferrals%2Ftree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_referrals_tree_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/referrals/tree/route.ts */ \"(rsc)/./src/app/api/referrals/tree/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/referrals/tree/route\",\n        pathname: \"/api/referrals/tree\",\n        filename: \"route\",\n        bundlePath: \"app/api/referrals/tree/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\referrals\\\\tree\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_referrals_tree_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freferrals%2Ftree%2Froute&page=%2Fapi%2Freferrals%2Ftree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freferrals%2Ftree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/referrals/tree/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/referrals/tree/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_referral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/referral */ \"(rsc)/./src/lib/referral.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n\n// GET - Fetch user's enhanced binary tree structure\nasync function GET(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const depth = parseInt(searchParams.get('depth') || '3');\n        const enhanced = searchParams.get('enhanced') === 'true';\n        const expandedNodes = searchParams.get('expanded') ? new Set(searchParams.get('expanded').split(',').filter((id)=>id.length > 0)) : new Set();\n        const nodeId = searchParams.get('nodeId'); // For loading specific node children\n        // If requesting specific node children\n        if (nodeId) {\n            const children = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_2__.loadNodeChildren)(nodeId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: children\n            });\n        }\n        // Get binary tree structure with expanded nodes - support infinite depth\n        const treeStructure = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_2__.getBinaryTreeStructure)(user.id, Math.min(depth, 50), expandedNodes); // Support much deeper trees\n        // Get user's referral statistics - use user table for direct referrals (sponsor relationships)\n        const directReferralsFromUserTable = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findMany({\n            where: {\n                referrerId: user.id\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                isActive: true,\n                createdAt: true\n            }\n        });\n        // Get binary tree placement data from referral table\n        const binaryPlacements = await _lib_database__WEBPACK_IMPORTED_MODULE_3__.referralDb.findByReferrerId(user.id);\n        const binaryPoints = await _lib_database__WEBPACK_IMPORTED_MODULE_3__.binaryPointsDb.findByUserId(user.id);\n        // Calculate placement statistics\n        const leftPlacements = binaryPlacements.filter((r)=>r.placementSide === 'LEFT').length;\n        const rightPlacements = binaryPlacements.filter((r)=>r.placementSide === 'RIGHT').length;\n        // Get total team counts (including all downliners)\n        const teamCounts = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_2__.getTotalTeamCount)(user.id);\n        // Calculate total commissions from transactions\n        const commissionTransactions = await _lib_database__WEBPACK_IMPORTED_MODULE_3__.transactionDb.findByUserId(user.id, {\n            types: [\n                'DIRECT_REFERRAL',\n                'BINARY_BONUS'\n            ],\n            status: 'COMPLETED'\n        });\n        const totalCommissions = commissionTransactions.reduce((sum, t)=>sum + t.amount, 0);\n        let responseData = {\n            treeStructure,\n            statistics: {\n                totalDirectReferrals: directReferralsFromUserTable.length,\n                leftPlacements,\n                rightPlacements,\n                leftReferrals: teamCounts.left,\n                rightReferrals: teamCounts.right,\n                leftTeam: teamCounts.left,\n                rightTeam: teamCounts.right,\n                totalTeam: teamCounts.total,\n                totalCommissions,\n                binaryPoints: binaryPoints || {\n                    leftPoints: 0,\n                    rightPoints: 0,\n                    matchedPoints: 0\n                }\n            },\n            referralLinks: {\n                left: `${\"http://localhost:3000\"}/register?ref=${user.referralId}&side=left`,\n                right: `${\"http://localhost:3000\"}/register?ref=${user.referralId}&side=right`,\n                general: `${\"http://localhost:3000\"}/register?ref=${user.referralId}`\n            }\n        };\n        // Add enhanced statistics if requested\n        if (enhanced) {\n            const detailedStats = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_2__.getDetailedTeamStats)(user.id);\n            const treeHealth = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_2__.getTreeHealthStats)(user.id);\n            responseData.statistics.detailedStats = detailedStats;\n            responseData.statistics.treeHealth = treeHealth;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: responseData\n        });\n    } catch (error) {\n        console.error('Binary tree fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch binary tree'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/referrals/tree/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30d';\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, 12);\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/miningUnitEarnings.ts":
/*!***************************************!*\
  !*** ./src/lib/miningUnitEarnings.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allocateEarningsToUnits: () => (/* binding */ allocateEarningsToUnits),\n/* harmony export */   calculateRemainingCapacity: () => (/* binding */ calculateRemainingCapacity),\n/* harmony export */   expireMiningUnit: () => (/* binding */ expireMiningUnit),\n/* harmony export */   getActiveMiningUnitsFIFO: () => (/* binding */ getActiveMiningUnitsFIFO),\n/* harmony export */   getMiningUnitEarningsHistory: () => (/* binding */ getMiningUnitEarningsHistory),\n/* harmony export */   getUserMiningUnitsWithEarnings: () => (/* binding */ getUserMiningUnitsWithEarnings),\n/* harmony export */   shouldExpireUnit: () => (/* binding */ shouldExpireUnit)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n/**\n * Get active mining units for a user ordered by creation date (FIFO)\n */ async function getActiveMiningUnitsFIFO(userId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n        where: {\n            userId,\n            status: 'ACTIVE',\n            expiryDate: {\n                gt: new Date()\n            }\n        },\n        orderBy: {\n            createdAt: 'asc'\n        }\n    });\n}\n/**\n * Calculate remaining earning capacity for a mining unit (5x - current earnings)\n */ function calculateRemainingCapacity(unit) {\n    const maxEarnings = unit.investmentAmount * 5;\n    const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    return Math.max(0, maxEarnings - currentTotalEarnings);\n}\n/**\n * Check if a mining unit should expire based on 5x earnings\n */ function shouldExpireUnit(unit) {\n    const maxEarnings = unit.investmentAmount * 5;\n    const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    return currentTotalEarnings >= maxEarnings;\n}\n/**\n * Allocate earnings to mining units using FIFO logic\n * Returns array of allocations showing how much was allocated to each unit\n */ async function allocateEarningsToUnits(userId, totalAmount, earningType, transactionId, description) {\n    const activeMiningUnits = await getActiveMiningUnitsFIFO(userId);\n    if (activeMiningUnits.length === 0) {\n        throw new Error('No active mining units found for earnings allocation');\n    }\n    const allocations = [];\n    let remainingAmount = totalAmount;\n    for (const unit of activeMiningUnits){\n        if (remainingAmount <= 0) break;\n        const remainingCapacity = calculateRemainingCapacity(unit);\n        if (remainingCapacity <= 0) {\n            continue;\n        }\n        // Allocate the minimum of remaining amount or remaining capacity\n        const allocationAmount = Math.min(remainingAmount, remainingCapacity);\n        if (allocationAmount > 0) {\n            // Update the mining unit earnings based on type\n            const updateData = {};\n            switch(earningType){\n                case 'MINING_EARNINGS':\n                    updateData.miningEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n                case 'DIRECT_REFERRAL':\n                    updateData.referralEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n                case 'BINARY_BONUS':\n                    updateData.binaryEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n            }\n            // Update total earned for legacy compatibility\n            updateData.totalEarned = {\n                increment: allocationAmount\n            };\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n                where: {\n                    id: unit.id\n                },\n                data: updateData\n            });\n            // Create earnings allocation record\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnitEarningsAllocation.create({\n                data: {\n                    miningUnitId: unit.id,\n                    transactionId,\n                    earningType,\n                    amount: allocationAmount,\n                    description\n                }\n            });\n            allocations.push({\n                miningUnitId: unit.id,\n                amount: allocationAmount,\n                remainingCapacity: remainingCapacity - allocationAmount\n            });\n            remainingAmount -= allocationAmount;\n            // Check if unit should expire after this allocation\n            const updatedUnit = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findUnique({\n                where: {\n                    id: unit.id\n                }\n            });\n            if (updatedUnit && shouldExpireUnit(updatedUnit)) {\n                await expireMiningUnit(unit.id, '5x_investment_reached');\n            }\n        }\n    }\n    // If there's still remaining amount, it means all units are at capacity\n    if (remainingAmount > 0) {\n        console.warn(`Unable to allocate ${remainingAmount} to mining units - all units at capacity`);\n        // Log this situation\n        await _lib_database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'EARNINGS_ALLOCATION_OVERFLOW',\n            userId,\n            details: {\n                totalAmount,\n                allocatedAmount: totalAmount - remainingAmount,\n                overflowAmount: remainingAmount,\n                earningType,\n                reason: 'all_units_at_capacity'\n            }\n        });\n    }\n    return allocations;\n}\n/**\n * Expire a mining unit and log the action\n */ async function expireMiningUnit(unitId, reason) {\n    const unit = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findUnique({\n        where: {\n            id: unitId\n        }\n    });\n    if (!unit) {\n        throw new Error(`Mining unit ${unitId} not found`);\n    }\n    await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n        where: {\n            id: unitId\n        },\n        data: {\n            status: 'EXPIRED'\n        }\n    });\n    const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    await _lib_database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n        action: 'MINING_UNIT_EXPIRED',\n        userId: unit.userId,\n        details: {\n            miningUnitId: unitId,\n            reason,\n            totalEarned: totalEarnings,\n            miningEarnings: unit.miningEarnings,\n            referralEarnings: unit.referralEarnings,\n            binaryEarnings: unit.binaryEarnings,\n            investmentAmount: unit.investmentAmount,\n            multiplier: totalEarnings / unit.investmentAmount\n        }\n    });\n    console.log(`Mining unit ${unitId} expired due to ${reason}. Total earnings: ${totalEarnings}`);\n}\n/**\n * Get detailed earnings breakdown for a user's mining units\n */ async function getUserMiningUnitsWithEarnings(userId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n        where: {\n            userId\n        },\n        orderBy: {\n            createdAt: 'asc'\n        }\n    });\n}\n/**\n * Get earnings allocation history for a mining unit\n */ async function getMiningUnitEarningsHistory(miningUnitId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnitEarningsAllocation.findMany({\n        where: {\n            miningUnitId\n        },\n        include: {\n            transaction: true\n        },\n        orderBy: {\n            allocatedAt: 'desc'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/miningUnitEarnings.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/referral.ts":
/*!*****************************!*\
  !*** ./src/lib/referral.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addBinaryPoints: () => (/* binding */ addBinaryPoints),\n/* harmony export */   bulkUpdateTreeCounts: () => (/* binding */ bulkUpdateTreeCounts),\n/* harmony export */   calculateDownlineCount: () => (/* binding */ calculateDownlineCount),\n/* harmony export */   getBinaryTreeStructure: () => (/* binding */ getBinaryTreeStructure),\n/* harmony export */   getCachedDownlineCounts: () => (/* binding */ getCachedDownlineCounts),\n/* harmony export */   getDetailedTeamStats: () => (/* binding */ getDetailedTeamStats),\n/* harmony export */   getDirectReferralCount: () => (/* binding */ getDirectReferralCount),\n/* harmony export */   getSponsorInfo: () => (/* binding */ getSponsorInfo),\n/* harmony export */   getTotalTeamCount: () => (/* binding */ getTotalTeamCount),\n/* harmony export */   getTreeHealthStats: () => (/* binding */ getTreeHealthStats),\n/* harmony export */   getUsersByGeneration: () => (/* binding */ getUsersByGeneration),\n/* harmony export */   hasActiveMiningUnits: () => (/* binding */ hasActiveMiningUnits),\n/* harmony export */   loadNodeChildren: () => (/* binding */ loadNodeChildren),\n/* harmony export */   placeUserByReferralType: () => (/* binding */ placeUserByReferralType),\n/* harmony export */   placeUserInBinaryTree: () => (/* binding */ placeUserInBinaryTree),\n/* harmony export */   placeUserInLeftSideOnly: () => (/* binding */ placeUserInLeftSideOnly),\n/* harmony export */   placeUserInRightSideOnly: () => (/* binding */ placeUserInRightSideOnly),\n/* harmony export */   placeUserInSpecificSide: () => (/* binding */ placeUserInSpecificSide),\n/* harmony export */   processBinaryMatching: () => (/* binding */ processBinaryMatching),\n/* harmony export */   processDirectReferralBonus: () => (/* binding */ processDirectReferralBonus),\n/* harmony export */   searchUsersInTree: () => (/* binding */ searchUsersInTree),\n/* harmony export */   updateCachedDownlineCounts: () => (/* binding */ updateCachedDownlineCounts)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n\n\n\n// Check if user has active mining units (for binary tree display)\nasync function hasActiveMiningUnits(userId) {\n    try {\n        const activeMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n        return activeMiningUnits > 0;\n    } catch (error) {\n        console.error('Error checking active mining units:', error);\n        return false;\n    }\n}\n// Calculate total downline count for a specific side\nasync function calculateDownlineCount(userId, side) {\n    try {\n        const downlineUsers = await getDownlineUsers(userId, side);\n        return downlineUsers.length;\n    } catch (error) {\n        console.error('Downline count calculation error:', error);\n        return 0;\n    }\n}\n// Find the optimal placement position in the weaker leg\nasync function findOptimalPlacementPosition(referrerId) {\n    try {\n        // Calculate total downline counts for both sides\n        const leftDownlineCount = await calculateDownlineCount(referrerId, 'LEFT');\n        const rightDownlineCount = await calculateDownlineCount(referrerId, 'RIGHT');\n        // Determine weaker leg based on total downline count\n        const weakerSide = leftDownlineCount <= rightDownlineCount ? 'LEFT' : 'RIGHT';\n        // Find the next available spot in the weaker leg\n        const availableSpot = await findNextAvailableSpotInLeg(referrerId, weakerSide);\n        if (availableSpot) {\n            return availableSpot;\n        }\n        // Fallback: if no spot found in weaker leg, try the other side\n        const strongerSide = weakerSide === 'LEFT' ? 'RIGHT' : 'LEFT';\n        const fallbackSpot = await findNextAvailableSpotInLeg(referrerId, strongerSide);\n        if (fallbackSpot) {\n            return fallbackSpot;\n        }\n        // Final fallback: place directly under referrer\n        const existingReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(referrerId);\n        const hasLeft = existingReferrals.some((r)=>r.placementSide === 'LEFT');\n        const hasRight = existingReferrals.some((r)=>r.placementSide === 'RIGHT');\n        if (!hasLeft) {\n            return {\n                userId: referrerId,\n                side: 'LEFT'\n            };\n        } else if (!hasRight) {\n            return {\n                userId: referrerId,\n                side: 'RIGHT'\n            };\n        }\n        // If both sides are occupied, place in the weaker side\n        return {\n            userId: referrerId,\n            side: weakerSide\n        };\n    } catch (error) {\n        console.error('Optimal placement position error:', error);\n        // Fallback to left side\n        return {\n            userId: referrerId,\n            side: 'LEFT'\n        };\n    }\n}\n// Enhanced place new user in binary tree with weaker leg algorithm\nasync function placeUserInBinaryTree(referrerId, newUserId) {\n    try {\n        // Find optimal placement position using advanced weaker leg algorithm\n        const optimalPosition = await findOptimalPlacementPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's left/right referral IDs\n        const updateData = optimalPosition.side === 'LEFT' ? {\n            leftReferralId: newUserId\n        } : {\n            rightReferralId: newUserId\n        };\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: updateData\n        });\n        // Create sponsor relationship (separate from binary placement)\n        // The sponsor is always the original referrer, regardless of binary placement\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Binary tree placement error:', error);\n        throw error;\n    }\n}\n// Create sponsor relationship (separate from binary placement)\nasync function createSponsorRelationship(sponsorId, newUserId) {\n    try {\n        // Update the new user's referrerId field to track sponsor\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: newUserId\n            },\n            data: {\n                referrerId: sponsorId\n            }\n        });\n        // Update sponsor's direct referral count\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: sponsorId\n            },\n            data: {\n                directReferralCount: {\n                    increment: 1\n                },\n                updatedAt: new Date()\n            }\n        });\n        // Mark referral as direct sponsor if the binary placement parent is the same as sponsor\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.updateMany({\n            where: {\n                referrerId: sponsorId,\n                referredId: newUserId\n            },\n            data: {\n                isDirectSponsor: true\n            }\n        });\n    } catch (error) {\n        console.error('Sponsor relationship creation error:', error);\n    // Don't throw error as this is supplementary to binary placement\n    }\n}\n// Update cached downline counts for a user\nasync function updateCachedDownlineCounts(userId) {\n    try {\n        const leftCount = await calculateDownlineCount(userId, 'LEFT');\n        const rightCount = await calculateDownlineCount(userId, 'RIGHT');\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                totalLeftDownline: leftCount,\n                totalRightDownline: rightCount,\n                lastTreeUpdate: new Date()\n            }\n        });\n    } catch (error) {\n        console.error('Update cached downline counts error:', error);\n    }\n}\n// Get cached downline counts (with fallback to real-time calculation)\nasync function getCachedDownlineCounts(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                totalLeftDownline: true,\n                totalRightDownline: true,\n                lastTreeUpdate: true\n            }\n        });\n        if (!user) {\n            return {\n                left: 0,\n                right: 0,\n                total: 0\n            };\n        }\n        // Check if cache is recent (within last 30 minutes for more accurate counts)\n        const cacheAge = user.lastTreeUpdate ? Date.now() - user.lastTreeUpdate.getTime() : Infinity;\n        const cacheValidDuration = 30 * 60 * 1000; // 30 minutes\n        if (cacheAge < cacheValidDuration && user.totalLeftDownline !== null && user.totalRightDownline !== null) {\n            // Use cached values\n            return {\n                left: user.totalLeftDownline,\n                right: user.totalRightDownline,\n                total: user.totalLeftDownline + user.totalRightDownline\n            };\n        } else {\n            // Cache is stale or missing, recalculate and update\n            const leftCount = await calculateDownlineCount(userId, 'LEFT');\n            const rightCount = await calculateDownlineCount(userId, 'RIGHT');\n            // Update cache asynchronously\n            updateCachedDownlineCounts(userId).catch(console.error);\n            return {\n                left: leftCount,\n                right: rightCount,\n                total: leftCount + rightCount\n            };\n        }\n    } catch (error) {\n        console.error('Get cached downline counts error:', error);\n        return {\n            left: 0,\n            right: 0,\n            total: 0\n        };\n    }\n}\n// Find optimal placement in specific side with weaker leg logic (LEGACY - for backward compatibility)\nasync function findOptimalPlacementInSide(referrerId, targetSide) {\n    try {\n        // First, try to find the next available spot in the target side\n        const availableSpot = await findNextAvailableSpotInLeg(referrerId, targetSide);\n        if (availableSpot) {\n            return availableSpot;\n        }\n        // If no spot available, find the position with smallest downline in that side\n        const sideUsers = await getDownlineUsers(referrerId, targetSide);\n        // Find the user with the smallest total downline in the target side\n        let optimalUser = referrerId;\n        let minDownlineCount = Infinity;\n        for (const sideUser of sideUsers){\n            const leftCount = await calculateDownlineCount(sideUser.id, 'LEFT');\n            const rightCount = await calculateDownlineCount(sideUser.id, 'RIGHT');\n            const totalDownline = leftCount + rightCount;\n            if (totalDownline < minDownlineCount) {\n                // Check if this user has available spots\n                const userReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(sideUser.id);\n                const hasLeft = userReferrals.some((r)=>r.placementSide === 'LEFT');\n                const hasRight = userReferrals.some((r)=>r.placementSide === 'RIGHT');\n                if (!hasLeft || !hasRight) {\n                    minDownlineCount = totalDownline;\n                    optimalUser = sideUser.id;\n                }\n            }\n        }\n        // Determine which side to place in for the optimal user\n        const optimalUserReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(optimalUser);\n        const hasLeft = optimalUserReferrals.some((r)=>r.placementSide === 'LEFT');\n        const hasRight = optimalUserReferrals.some((r)=>r.placementSide === 'RIGHT');\n        if (!hasLeft) {\n            return {\n                userId: optimalUser,\n                side: 'LEFT'\n            };\n        } else if (!hasRight) {\n            return {\n                userId: optimalUser,\n                side: 'RIGHT'\n            };\n        }\n        // If both sides occupied, use weaker leg logic\n        const leftCount = await calculateDownlineCount(optimalUser, 'LEFT');\n        const rightCount = await calculateDownlineCount(optimalUser, 'RIGHT');\n        const weakerSide = leftCount <= rightCount ? 'LEFT' : 'RIGHT';\n        return {\n            userId: optimalUser,\n            side: weakerSide\n        };\n    } catch (error) {\n        console.error('Optimal placement in side error:', error);\n        return {\n            userId: referrerId,\n            side: targetSide\n        };\n    }\n}\n// NEW: Find deepest available position in LEFT side only (strict left-side placement)\nasync function findDeepestLeftPosition(referrerId) {\n    try {\n        // Start from the referrer and traverse down the LEFT side only\n        let currentUserId = referrerId;\n        let currentLevel = 0;\n        const maxDepth = 20; // Prevent infinite loops\n        while(currentLevel < maxDepth){\n            // Verify current user exists\n            const userExists = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true\n                }\n            });\n            if (!userExists) {\n                // User doesn't exist, fallback to referrer\n                return {\n                    userId: referrerId,\n                    side: 'LEFT'\n                };\n            }\n            // Check if current user has a LEFT spot available\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            const hasLeft = currentReferrals.some((r)=>r.placementSide === 'LEFT');\n            if (!hasLeft) {\n                // Found an available LEFT spot\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            // Move to the LEFT child and continue traversing\n            const leftChild = currentReferrals.find((r)=>r.placementSide === 'LEFT');\n            if (!leftChild) {\n                // This shouldn't happen if hasLeft is true, but safety check\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            currentUserId = leftChild.referredId;\n            currentLevel++;\n        }\n        // If we've reached max depth, place at the last position\n        return {\n            userId: currentUserId,\n            side: 'LEFT'\n        };\n    } catch (error) {\n        console.error('Find deepest left position error:', error);\n        return {\n            userId: referrerId,\n            side: 'LEFT'\n        };\n    }\n}\n// NEW: Find deepest available position in RIGHT side only (strict right-side placement)\nasync function findDeepestRightPosition(referrerId) {\n    try {\n        // Start from the referrer and traverse down the RIGHT side only\n        let currentUserId = referrerId;\n        let currentLevel = 0;\n        const maxDepth = 20; // Prevent infinite loops\n        while(currentLevel < maxDepth){\n            // Verify current user exists\n            const userExists = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true\n                }\n            });\n            if (!userExists) {\n                // User doesn't exist, fallback to referrer\n                return {\n                    userId: referrerId,\n                    side: 'RIGHT'\n                };\n            }\n            // Check if current user has a RIGHT spot available\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            const hasRight = currentReferrals.some((r)=>r.placementSide === 'RIGHT');\n            if (!hasRight) {\n                // Found an available RIGHT spot\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            // Move to the RIGHT child and continue traversing\n            const rightChild = currentReferrals.find((r)=>r.placementSide === 'RIGHT');\n            if (!rightChild) {\n                // This shouldn't happen if hasRight is true, but safety check\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            currentUserId = rightChild.referredId;\n            currentLevel++;\n        }\n        // If we've reached max depth, place at the last position\n        return {\n            userId: currentUserId,\n            side: 'RIGHT'\n        };\n    } catch (error) {\n        console.error('Find deepest right position error:', error);\n        return {\n            userId: referrerId,\n            side: 'RIGHT'\n        };\n    }\n}\n// Enhanced place user in specific side with weaker leg algorithm (LEGACY - for backward compatibility)\nasync function placeUserInSpecificSide(referrerId, newUserId, side) {\n    try {\n        // Find optimal placement position within the specified side\n        const optimalPosition = await findOptimalPlacementInSide(referrerId, side);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's referral ID\n        const updateData = optimalPosition.side === 'LEFT' ? {\n            leftReferralId: newUserId\n        } : {\n            rightReferralId: newUserId\n        };\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: updateData\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Specific side placement error:', error);\n        throw error;\n    }\n}\n// NEW: Place user strictly in LEFT side only (deepest available left position)\nasync function placeUserInLeftSideOnly(referrerId, newUserId) {\n    try {\n        // Find the deepest available position in the LEFT side\n        const optimalPosition = await findDeepestLeftPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's left referral ID\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: {\n                leftReferralId: newUserId\n            }\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Left side only placement error:', error);\n        throw error;\n    }\n}\n// NEW: Place user strictly in RIGHT side only (deepest available right position)\nasync function placeUserInRightSideOnly(referrerId, newUserId) {\n    try {\n        // Find the deepest available position in the RIGHT side\n        const optimalPosition = await findDeepestRightPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's right referral ID\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: {\n                rightReferralId: newUserId\n            }\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Right side only placement error:', error);\n        throw error;\n    }\n}\n// NEW: Main placement function that routes to appropriate algorithm based on referral link type\nasync function placeUserByReferralType(referrerId, newUserId, referralType) {\n    try {\n        switch(referralType){\n            case 'left':\n                // Strict left-side placement: find deepest available left position\n                return await placeUserInLeftSideOnly(referrerId, newUserId);\n            case 'right':\n                // Strict right-side placement: find deepest available right position\n                return await placeUserInRightSideOnly(referrerId, newUserId);\n            case 'general':\n            default:\n                // Default weaker leg placement\n                return await placeUserInBinaryTree(referrerId, newUserId);\n        }\n    } catch (error) {\n        console.error('Placement by referral type error:', error);\n        throw error;\n    }\n}\n// Find next available spot in a specific leg\nasync function findNextAvailableSpotInLeg(rootUserId, targetSide) {\n    try {\n        // Get the first user in the target leg\n        const rootReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(rootUserId);\n        const firstInLeg = rootReferrals.find((r)=>r.placementSide === targetSide);\n        if (!firstInLeg) {\n            // The target side is completely empty\n            return {\n                userId: rootUserId,\n                side: targetSide\n            };\n        }\n        // Traverse down the leg to find the first available spot\n        const queue = [\n            firstInLeg.referredId\n        ];\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            // Check if this user has any empty spots\n            const hasLeft = currentReferrals.some((r)=>r.placementSide === 'LEFT');\n            const hasRight = currentReferrals.some((r)=>r.placementSide === 'RIGHT');\n            if (!hasLeft) {\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            if (!hasRight) {\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            // Add children to queue for further traversal\n            currentReferrals.forEach((r)=>{\n                queue.push(r.referredId);\n            });\n        }\n        return null; // No available spot found\n    } catch (error) {\n        console.error('Find available spot error:', error);\n        return null;\n    }\n}\n// Process direct referral bonus (10% of investment) - Added directly to sponsor's wallet\n// ONLY active sponsors (with active mining units) receive commissions\n// ONLY paid ONCE per user (first purchase only)\nasync function processDirectReferralBonus(referrerId, investmentAmount, purchaserId) {\n    try {\n        // Check if sponsor is active (has active mining units)\n        const isActive = await hasActiveMiningUnits(referrerId);\n        if (!isActive) {\n            console.log(`Skipping direct referral bonus for inactive sponsor ${referrerId} - no active mining units`);\n            return 0; // Return 0 commission for inactive sponsors\n        }\n        // Check if this user has already received first commission from this purchaser\n        if (purchaserId) {\n            const purchaser = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: purchaserId\n                },\n                select: {\n                    hasReceivedFirstCommission: true,\n                    firstName: true,\n                    lastName: true\n                }\n            });\n            if (purchaser?.hasReceivedFirstCommission) {\n                console.log(`Skipping direct referral bonus - sponsor ${referrerId} already received first commission from user ${purchaserId} (${purchaser.firstName} ${purchaser.lastName})`);\n                return 0; // Return 0 commission for subsequent purchases\n            }\n        }\n        const bonusPercentage = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('DIRECT_REFERRAL_BONUS') || '10');\n        const bonusAmount = investmentAmount * bonusPercentage / 100;\n        // Create direct referral transaction first\n        const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n            userId: referrerId,\n            type: 'DIRECT_REFERRAL',\n            amount: bonusAmount,\n            description: `Direct referral bonus (${bonusPercentage}% of $${investmentAmount}) - First purchase`,\n            reference: purchaserId ? `from_user:${purchaserId}` : 'direct_referral',\n            status: 'COMPLETED'\n        });\n        // Allocate earnings to sponsor's mining units using FIFO logic\n        try {\n            const allocations = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(referrerId, bonusAmount, 'DIRECT_REFERRAL', transaction.id, `Direct referral commission from ${purchaserId ? 'user purchase' : 'referral'}`);\n            console.log(`Allocated ${bonusAmount} referral bonus to ${allocations.length} mining units for sponsor ${referrerId}`);\n            // Always add to wallet balance regardless of mining unit allocation\n            // This ensures the commission is available for withdrawal\n            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(referrerId, bonusAmount);\n            console.log(`Added ${bonusAmount} referral bonus to wallet balance for sponsor ${referrerId}`);\n        } catch (allocationError) {\n            console.error(`Failed to allocate referral bonus to mining units for ${referrerId}:`, allocationError);\n            // Fallback: Add to wallet balance if allocation fails\n            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(referrerId, bonusAmount);\n            console.log(`Fallback: Added ${bonusAmount} referral bonus directly to wallet for sponsor ${referrerId}`);\n        }\n        // Update referral commission earned\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.updateMany({\n            where: {\n                referrerId,\n                referred: {\n                    miningUnits: {\n                        some: {\n                            investmentAmount\n                        }\n                    }\n                }\n            },\n            data: {\n                commissionEarned: {\n                    increment: bonusAmount\n                }\n            }\n        });\n        // Mark the purchaser as having received their first commission\n        if (purchaserId) {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n                where: {\n                    id: purchaserId\n                },\n                data: {\n                    hasReceivedFirstCommission: true\n                }\n            });\n        }\n        console.log(`First-time direct referral bonus of $${bonusAmount} awarded to active sponsor ${referrerId} from user ${purchaserId}`);\n        return bonusAmount;\n    } catch (error) {\n        console.error('Direct referral bonus error:', error);\n        throw error;\n    }\n}\n// Add points to binary system when someone makes an investment ($100 = 1 point)\nasync function addBinaryPoints(userId, investmentAmount) {\n    try {\n        // Calculate points: $100 investment = 1 point (with 2 decimal precision)\n        // $150 = 1.5 points, $250 = 2.5 points, etc.\n        const points = Math.round(investmentAmount / 100 * 100) / 100; // Round to 2 decimal places\n        if (points <= 0) return; // No points to add if investment is less than $100\n        // Find all upline users and add points to their binary system (ONLY active upliners)\n        const uplineUsers = await getUplineUsers(userId);\n        for (const uplineUser of uplineUsers){\n            // Check if upline user is active (has active mining units)\n            const isActive = await hasActiveMiningUnits(uplineUser.id);\n            if (!isActive) {\n                console.log(`Skipping inactive user ${uplineUser.id} - no active mining units`);\n                continue; // Skip inactive users\n            }\n            // Determine which side this user is on relative to upline\n            const placementSide = await getUserPlacementSide(uplineUser.id, userId);\n            if (placementSide) {\n                // Get current binary points for this user\n                const currentBinaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(uplineUser.id);\n                // Get max points per side setting\n                const maxPointsPerSide = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '10');\n                // Check current points on the target side\n                const currentLeftPoints = currentBinaryPoints?.leftPoints || 0;\n                const currentRightPoints = currentBinaryPoints?.rightPoints || 0;\n                let pointsToAdd = 0;\n                let sideToUpdate = placementSide;\n                if (placementSide === 'LEFT') {\n                    // Check if left side has reached the maximum\n                    if (currentLeftPoints >= maxPointsPerSide) {\n                        console.log(`User ${uplineUser.id} left side has reached maximum (${currentLeftPoints}/${maxPointsPerSide}). No points added.`);\n                        continue; // Skip adding points to this user\n                    }\n                    // Calculate how many points can be added without exceeding the limit\n                    pointsToAdd = Math.min(points, maxPointsPerSide - currentLeftPoints);\n                } else {\n                    // Check if right side has reached the maximum\n                    if (currentRightPoints >= maxPointsPerSide) {\n                        console.log(`User ${uplineUser.id} right side has reached maximum (${currentRightPoints}/${maxPointsPerSide}). No points added.`);\n                        continue; // Skip adding points to this user\n                    }\n                    // Calculate how many points can be added without exceeding the limit\n                    pointsToAdd = Math.min(points, maxPointsPerSide - currentRightPoints);\n                }\n                // Only add points if there's room\n                if (pointsToAdd > 0) {\n                    const updateData = placementSide === 'LEFT' ? {\n                        leftPoints: pointsToAdd\n                    } : {\n                        rightPoints: pointsToAdd\n                    };\n                    await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.upsert({\n                        userId: uplineUser.id,\n                        ...updateData\n                    });\n                    console.log(`Added ${pointsToAdd} points to ${placementSide} side for active user ${uplineUser.id} (${pointsToAdd < points ? 'capped at limit' : 'full amount'})`);\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Binary points addition error:', error);\n        throw error;\n    }\n}\n// Get all upline users for a given user\nasync function getUplineUsers(userId) {\n    try {\n        const uplineUsers = [];\n        let currentUserId = userId;\n        // Traverse up the tree (maximum 10 levels to prevent infinite loops)\n        for(let level = 0; level < 10; level++){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                },\n                include: {\n                    referrer: {\n                        select: {\n                            id: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            if (!referral) break;\n            uplineUsers.push(referral.referrer);\n            currentUserId = referral.referrerId;\n        }\n        return uplineUsers;\n    } catch (error) {\n        console.error('Upline users fetch error:', error);\n        return [];\n    }\n}\n// Get all ACTIVE upline users for a given user (skip inactive users)\nasync function getActiveUplineUsers(userId) {\n    try {\n        const uplineUsers = [];\n        let currentUserId = userId;\n        // Traverse up the tree (maximum 10 levels to prevent infinite loops)\n        for(let level = 0; level < 10; level++){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                },\n                include: {\n                    referrer: {\n                        select: {\n                            id: true,\n                            email: true,\n                            isActive: true\n                        }\n                    }\n                }\n            });\n            if (!referral) break;\n            // Only add active users to the list\n            if (referral.referrer.isActive) {\n                uplineUsers.push(referral.referrer);\n            }\n            // Continue traversing up regardless of active status\n            currentUserId = referral.referrerId;\n        }\n        return uplineUsers;\n    } catch (error) {\n        console.error('Active upline users fetch error:', error);\n        return [];\n    }\n}\n// Determine which side a user is on relative to an upline user\nasync function getUserPlacementSide(uplineUserId, userId) {\n    try {\n        // Check direct placement first\n        const directReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: uplineUserId,\n                referredId: userId\n            }\n        });\n        if (directReferral) {\n            return directReferral.placementSide;\n        }\n        // Check indirect placement by traversing down the tree\n        const leftSideUsers = await getDownlineUsers(uplineUserId, 'LEFT');\n        const rightSideUsers = await getDownlineUsers(uplineUserId, 'RIGHT');\n        if (leftSideUsers.some((u)=>u.id === userId)) {\n            return 'LEFT';\n        }\n        if (rightSideUsers.some((u)=>u.id === userId)) {\n            return 'RIGHT';\n        }\n        return null;\n    } catch (error) {\n        console.error('Placement side determination error:', error);\n        return null;\n    }\n}\n// Get all downline users for a specific side\nasync function getDownlineUsers(userId, side) {\n    try {\n        const downlineUsers = [];\n        const visited = new Set();\n        // Start with the direct placement on the specified side\n        const initialReferrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId: userId,\n                placementSide: side\n            },\n            select: {\n                referredId: true\n            }\n        });\n        // Use BFS to traverse the entire subtree\n        const queue = initialReferrals.map((r)=>r.referredId);\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            // Skip if already visited (prevent infinite loops)\n            if (visited.has(currentUserId)) continue;\n            visited.add(currentUserId);\n            // Add current user to downline\n            downlineUsers.push({\n                id: currentUserId\n            });\n            // Get all referrals (both LEFT and RIGHT) from current user\n            const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                where: {\n                    referrerId: currentUserId\n                },\n                select: {\n                    referredId: true\n                }\n            });\n            // Add all children to queue for further traversal\n            for (const referral of referrals){\n                if (!visited.has(referral.referredId)) {\n                    queue.push(referral.referredId);\n                }\n            }\n        }\n        return downlineUsers;\n    } catch (error) {\n        console.error('Downline users fetch error:', error);\n        return [];\n    }\n}\n// Get all downline users (both sides combined) for total team count\nasync function getAllDownlineUsers(userId) {\n    try {\n        const downlineUsers = [];\n        const visited = new Set();\n        // Get all direct referrals (both LEFT and RIGHT)\n        const initialReferrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId: userId\n            },\n            select: {\n                referredId: true\n            }\n        });\n        // Use BFS to traverse the entire binary tree\n        const queue = initialReferrals.map((r)=>r.referredId);\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            // Skip if already visited (prevent infinite loops)\n            if (visited.has(currentUserId)) continue;\n            visited.add(currentUserId);\n            // Get user info including active status\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true,\n                    isActive: true\n                }\n            });\n            if (user) {\n                downlineUsers.push({\n                    id: user.id,\n                    isActive: user.isActive\n                });\n                // Get all referrals from current user\n                const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                    where: {\n                        referrerId: currentUserId\n                    },\n                    select: {\n                        referredId: true\n                    }\n                });\n                // Add all children to queue for further traversal\n                for (const referral of referrals){\n                    if (!visited.has(referral.referredId)) {\n                        queue.push(referral.referredId);\n                    }\n                }\n            }\n        }\n        return downlineUsers;\n    } catch (error) {\n        console.error('All downline users fetch error:', error);\n        return [];\n    }\n}\n// Process weekly binary matching (15:00 UTC on Saturdays)\nasync function processBinaryMatching() {\n    try {\n        console.log('Starting binary matching process...');\n        const maxPointsPerSide = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '10');\n        const pointValue = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('BINARY_POINT_VALUE') || '10'); // Dynamic point value from settings\n        // Get all users with binary points\n        const usersWithPoints = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findMany({\n            where: {\n                OR: [\n                    {\n                        leftPoints: {\n                            gt: 0\n                        }\n                    },\n                    {\n                        rightPoints: {\n                            gt: 0\n                        }\n                    }\n                ]\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        console.log(`Processing binary matching for ${usersWithPoints.length} users`);\n        const matchingResults = [];\n        for (const userPoints of usersWithPoints){\n            try {\n                // Calculate matching points (minimum of left and right, capped at max per side)\n                const leftPoints = Math.min(userPoints.leftPoints, maxPointsPerSide);\n                const rightPoints = Math.min(userPoints.rightPoints, maxPointsPerSide);\n                const matchedPoints = Math.min(leftPoints, rightPoints);\n                if (matchedPoints > 0) {\n                    // Calculate direct payout: 1 point = $10\n                    const userPayout = matchedPoints * pointValue;\n                    try {\n                        // Create binary bonus transaction first\n                        const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n                            userId: userPoints.userId,\n                            type: 'BINARY_BONUS',\n                            amount: userPayout,\n                            description: `Binary matching bonus - ${matchedPoints} points matched at $${pointValue} per point`,\n                            status: 'COMPLETED'\n                        });\n                        // Allocate earnings to user's mining units using FIFO logic\n                        try {\n                            const allocations = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(userPoints.userId, userPayout, 'BINARY_BONUS', transaction.id, `Binary matching bonus - ${matchedPoints} points matched`);\n                            console.log(`Allocated ${userPayout} binary bonus to ${allocations.length} mining units for user ${userPoints.userId}`);\n                            // Always add to wallet balance regardless of mining unit allocation\n                            // This ensures the bonus is available for withdrawal\n                            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(userPoints.userId, userPayout);\n                            console.log(`Added ${userPayout} binary bonus to wallet balance for user ${userPoints.userId}`);\n                        } catch (allocationError) {\n                            console.error(`Failed to allocate binary bonus to mining units for ${userPoints.userId}:`, allocationError);\n                            // Fallback: Add to wallet balance if allocation fails\n                            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(userPoints.userId, userPayout);\n                            console.log(`Fallback: Added ${userPayout} binary bonus directly to wallet for user ${userPoints.userId}`);\n                        }\n                        // Calculate remaining points after matching - reset weaker side to 0\n                        // Example: User has 7 left, 5 right -> 5 matched, left becomes 2, right becomes 0\n                        const remainingLeftPoints = Math.max(0, userPoints.leftPoints - matchedPoints);\n                        const remainingRightPoints = Math.max(0, userPoints.rightPoints - matchedPoints);\n                        // Reset the weaker side to 0 after matching (proper binary matching rule)\n                        const finalLeftPoints = userPoints.leftPoints > userPoints.rightPoints ? remainingLeftPoints : 0;\n                        const finalRightPoints = userPoints.rightPoints > userPoints.leftPoints ? remainingRightPoints : 0;\n                        // Update binary points - reset weaker side to 0, keep stronger side remainder\n                        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n                            where: {\n                                id: userPoints.id\n                            },\n                            data: {\n                                leftPoints: finalLeftPoints,\n                                rightPoints: finalRightPoints,\n                                matchedPoints: {\n                                    increment: matchedPoints\n                                },\n                                totalMatched: {\n                                    increment: matchedPoints\n                                },\n                                lastMatchDate: new Date(),\n                                flushDate: new Date()\n                            }\n                        });\n                        matchingResults.push({\n                            userId: userPoints.userId,\n                            matchedPoints,\n                            payout: userPayout,\n                            remainingLeftPoints: finalLeftPoints,\n                            remainingRightPoints: finalRightPoints\n                        });\n                        console.log(`User ${userPoints.userId}: ${matchedPoints} points matched, $${userPayout.toFixed(2)} payout, remaining: L${finalLeftPoints} R${finalRightPoints}`);\n                    } catch (payoutError) {\n                        console.error(`Error processing payout for user ${userPoints.userId}:`, payoutError);\n                    // Continue with next user instead of failing the entire process\n                    }\n                } else {\n                    // No matching possible, but still reset excess points if over the limit\n                    const excessLeft = Math.max(0, userPoints.leftPoints - maxPointsPerSide);\n                    const excessRight = Math.max(0, userPoints.rightPoints - maxPointsPerSide);\n                    if (excessLeft > 0 || excessRight > 0) {\n                        try {\n                            // Reset excess points (pressure out)\n                            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n                                where: {\n                                    id: userPoints.id\n                                },\n                                data: {\n                                    leftPoints: Math.min(userPoints.leftPoints, maxPointsPerSide),\n                                    rightPoints: Math.min(userPoints.rightPoints, maxPointsPerSide),\n                                    flushDate: new Date()\n                                }\n                            });\n                            console.log(`User ${userPoints.userId}: Excess points reset - L${excessLeft} R${excessRight} points flushed`);\n                        } catch (flushError) {\n                            console.error(`Error flushing excess points for user ${userPoints.userId}:`, flushError);\n                        }\n                    }\n                }\n            } catch (userError) {\n                console.error(`Error processing binary matching for user ${userPoints.userId}:`, userError);\n            }\n        }\n        // Log the binary matching process\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'BINARY_MATCHING_PROCESSED',\n            details: {\n                usersProcessed: usersWithPoints.length,\n                totalMatchedPoints: matchingResults.reduce((sum, r)=>sum + r.matchedPoints, 0),\n                pointValue,\n                totalPayouts: matchingResults.reduce((sum, r)=>sum + r.payout, 0),\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Binary matching completed. Processed ${matchingResults.length} users with total payouts: $${matchingResults.reduce((sum, r)=>sum + r.payout, 0).toFixed(2)}`);\n        return {\n            success: true,\n            usersProcessed: matchingResults.length,\n            totalPayouts: matchingResults.reduce((sum, r)=>sum + r.payout, 0),\n            matchingResults\n        };\n    } catch (error) {\n        console.error('Binary matching process error:', error);\n        throw error;\n    }\n}\n// Get sponsor information for a user\nasync function getSponsorInfo(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                referrerId: true\n            }\n        });\n        if (!user?.referrerId) return null;\n        const sponsor = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: user.referrerId\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true\n            }\n        });\n        return sponsor;\n    } catch (error) {\n        console.error('Sponsor info fetch error:', error);\n        return null;\n    }\n}\n// Get direct referral count for a user (sponsored users)\nasync function getDirectReferralCount(userId) {\n    try {\n        const count = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count({\n            where: {\n                referrerId: userId\n            }\n        });\n        return count;\n    } catch (error) {\n        console.error('Direct referral count error:', error);\n        return 0;\n    }\n}\n// Get total team count (all downline users in binary tree) - uses cached values\nasync function getTotalTeamCount(userId) {\n    try {\n        return await getCachedDownlineCounts(userId);\n    } catch (error) {\n        console.error('Total team count error:', error);\n        return {\n            left: 0,\n            right: 0,\n            total: 0\n        };\n    }\n}\n// Get detailed team statistics\nasync function getDetailedTeamStats(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                directReferralCount: true\n            }\n        });\n        const teamCounts = await getCachedDownlineCounts(userId);\n        // Get all downline users for accurate active member count\n        const allDownlineUsers = await getAllDownlineUsers(userId);\n        const activeMembers = allDownlineUsers.filter((u)=>u.isActive).length;\n        // Get recent joins (last 30 days) - direct referrals only\n        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n        const recentJoins = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count({\n            where: {\n                referrerId: userId,\n                createdAt: {\n                    gte: thirtyDaysAgo\n                }\n            }\n        });\n        return {\n            directReferrals: user?.directReferralCount || 0,\n            leftTeam: teamCounts.left,\n            rightTeam: teamCounts.right,\n            totalTeam: teamCounts.total,\n            activeMembers,\n            recentJoins\n        };\n    } catch (error) {\n        console.error('Detailed team stats error:', error);\n        return {\n            directReferrals: 0,\n            leftTeam: 0,\n            rightTeam: 0,\n            totalTeam: 0,\n            activeMembers: 0,\n            recentJoins: 0\n        };\n    }\n}\n// Find all users in a specific generation (level) of the tree\nasync function getUsersByGeneration(userId, generation) {\n    try {\n        if (generation <= 0) return [];\n        let currentLevelUsers = [\n            {\n                id: userId,\n                side: null\n            }\n        ];\n        for(let level = 1; level <= generation; level++){\n            const nextLevelUsers = [];\n            for (const currentUser of currentLevelUsers){\n                const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                    where: {\n                        referrerId: currentUser.id\n                    },\n                    include: {\n                        referred: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true,\n                                createdAt: true\n                            }\n                        }\n                    }\n                });\n                for (const referral of referrals){\n                    nextLevelUsers.push({\n                        id: referral.referredId,\n                        side: referral.placementSide\n                    });\n                }\n            }\n            currentLevelUsers = nextLevelUsers;\n        }\n        // Get full user details for the final generation\n        const userDetails = await Promise.all(currentLevelUsers.map(async (user)=>{\n            const userInfo = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: user.id\n                },\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true,\n                    createdAt: true\n                }\n            });\n            return {\n                ...userInfo,\n                placementSide: user.side\n            };\n        }));\n        return userDetails.filter(Boolean);\n    } catch (error) {\n        console.error('Users by generation error:', error);\n        return [];\n    }\n}\n// Enhanced binary tree structure with detailed member information\nasync function getBinaryTreeStructure(userId, depth = 3, expandedNodes = new Set()) {\n    try {\n        const buildTree = async (currentUserId, currentDepth, path = '')=>{\n            if (currentDepth <= 0) return null;\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true,\n                    profilePicture: true,\n                    createdAt: true\n                }\n            });\n            if (!user) return null;\n            // Check if user has active mining units (for binary tree display)\n            const isActive = await hasActiveMiningUnits(currentUserId);\n            // Get sponsor information\n            const sponsorInfo = await getSponsorInfo(currentUserId);\n            // Get direct referral count\n            const directReferralCount = await getDirectReferralCount(currentUserId);\n            // Get team counts\n            const teamCounts = await getTotalTeamCount(currentUserId);\n            // Get direct referrals (binary placement)\n            const leftReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: currentUserId,\n                    placementSide: 'LEFT'\n                },\n                include: {\n                    referred: {\n                        select: {\n                            id: true,\n                            email: true,\n                            firstName: true,\n                            lastName: true,\n                            createdAt: true\n                        }\n                    }\n                }\n            });\n            const rightReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: currentUserId,\n                    placementSide: 'RIGHT'\n                },\n                include: {\n                    referred: {\n                        select: {\n                            id: true,\n                            email: true,\n                            firstName: true,\n                            lastName: true,\n                            createdAt: true\n                        }\n                    }\n                }\n            });\n            // Get binary points\n            const binaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(currentUserId);\n            // Determine if we should load children for infinite depth support\n            // Load children if we have remaining depth AND either:\n            // 1. We're within the initial depth (first 3 levels) - always show first 3 levels\n            // 2. OR this node is explicitly expanded - show children of expanded nodes\n            const isWithinInitialDepth = path.length < 3; // First 3 levels (root = 0, level 1 = 1 char, level 2 = 2 chars)\n            const isNodeExpanded = expandedNodes.has(currentUserId);\n            const shouldLoadChildren = currentDepth > 1 && (isWithinInitialDepth || isNodeExpanded);\n            // Check if children exist (for showing expand button)\n            const hasLeftChild = leftReferral !== null;\n            const hasRightChild = rightReferral !== null;\n            return {\n                user: {\n                    ...user,\n                    isActive\n                },\n                sponsorInfo,\n                directReferralCount,\n                teamCounts,\n                binaryPoints: binaryPoints || {\n                    leftPoints: 0,\n                    rightPoints: 0,\n                    matchedPoints: 0\n                },\n                hasLeftChild,\n                hasRightChild,\n                leftChild: shouldLoadChildren && leftReferral ? await buildTree(leftReferral.referredId, currentDepth - 1, path + 'L') : null,\n                rightChild: shouldLoadChildren && rightReferral ? await buildTree(rightReferral.referredId, currentDepth - 1, path + 'R') : null\n            };\n        };\n        return await buildTree(userId, depth);\n    } catch (error) {\n        console.error('Binary tree structure error:', error);\n        throw error;\n    }\n}\n// Load children for a specific node (for dynamic expansion)\nasync function loadNodeChildren(userId) {\n    try {\n        // Get direct referrals (binary placement)\n        const leftReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: userId,\n                placementSide: 'LEFT'\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        profilePicture: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n        const rightReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: userId,\n                placementSide: 'RIGHT'\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        profilePicture: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n        const buildChildNode = async (referral)=>{\n            if (!referral) return null;\n            const childUserId = referral.referredId;\n            // Check if user has active mining units (for binary tree display)\n            const isActive = await hasActiveMiningUnits(childUserId);\n            // Get sponsor information\n            const sponsorInfo = await getSponsorInfo(childUserId);\n            // Get direct referral count\n            const directReferralCount = await getDirectReferralCount(childUserId);\n            // Get team counts\n            const teamCounts = await getTotalTeamCount(childUserId);\n            // Get binary points\n            const binaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(childUserId);\n            // Check if this child has its own children\n            const hasLeftChild = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: childUserId,\n                    placementSide: 'LEFT'\n                },\n                select: {\n                    id: true\n                }\n            }) !== null;\n            const hasRightChild = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: childUserId,\n                    placementSide: 'RIGHT'\n                },\n                select: {\n                    id: true\n                }\n            }) !== null;\n            return {\n                user: {\n                    ...referral.referred,\n                    isActive\n                },\n                sponsorInfo,\n                directReferralCount,\n                teamCounts,\n                binaryPoints: binaryPoints || {\n                    leftPoints: 0,\n                    rightPoints: 0,\n                    matchedPoints: 0\n                },\n                hasLeftChild,\n                hasRightChild,\n                leftChild: null,\n                rightChild: null\n            };\n        };\n        const leftChild = await buildChildNode(leftReferral);\n        const rightChild = await buildChildNode(rightReferral);\n        return {\n            leftChild,\n            rightChild\n        };\n    } catch (error) {\n        console.error('Load node children error:', error);\n        return {\n            leftChild: null,\n            rightChild: null\n        };\n    }\n}\n// Search for users in the binary tree\nasync function searchUsersInTree(rootUserId, searchTerm, maxResults = 20) {\n    try {\n        const searchPattern = `%${searchTerm.toLowerCase()}%`;\n        // Get all downline users that match the search term\n        const leftUsers = await getDownlineUsers(rootUserId, 'LEFT');\n        const rightUsers = await getDownlineUsers(rootUserId, 'RIGHT');\n        const allDownlineIds = [\n            ...leftUsers,\n            ...rightUsers\n        ].map((u)=>u.id);\n        if (allDownlineIds.length === 0) return [];\n        const matchingUsers = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            where: {\n                id: {\n                    in: allDownlineIds\n                },\n                OR: [\n                    {\n                        email: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    },\n                    {\n                        firstName: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    },\n                    {\n                        lastName: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    }\n                ]\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                createdAt: true,\n                referrerId: true\n            },\n            take: maxResults\n        });\n        // Get placement path and sponsor info for each user\n        const results = await Promise.all(matchingUsers.map(async (user)=>{\n            const placementPath = await getPlacementPath(rootUserId, user.id);\n            const generation = placementPath.split('-').length;\n            let sponsorInfo = undefined;\n            if (user.referrerId) {\n                sponsorInfo = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                    where: {\n                        id: user.referrerId\n                    },\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                });\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                firstName: user.firstName,\n                lastName: user.lastName,\n                createdAt: user.createdAt,\n                placementPath,\n                generation,\n                sponsorInfo: sponsorInfo || undefined\n            };\n        }));\n        return results;\n    } catch (error) {\n        console.error('Search users in tree error:', error);\n        return [];\n    }\n}\n// Get placement path from root to a specific user (e.g., \"L-R-L\")\nasync function getPlacementPath(rootUserId, targetUserId) {\n    try {\n        if (rootUserId === targetUserId) return 'ROOT';\n        const path = [];\n        let currentUserId = targetUserId;\n        // Traverse up the tree to find path\n        while(currentUserId !== rootUserId){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                }\n            });\n            if (!referral) break;\n            path.unshift(referral.placementSide === 'LEFT' ? 'L' : 'R');\n            currentUserId = referral.referrerId;\n            // Prevent infinite loops\n            if (path.length > 20) break;\n        }\n        return path.join('-') || 'UNKNOWN';\n    } catch (error) {\n        console.error('Get placement path error:', error);\n        return 'UNKNOWN';\n    }\n}\n// Update tree counts after a new user placement\nasync function updateTreeCountsAfterPlacement(sponsorId, placementParentId) {\n    try {\n        // Update counts for the sponsor (if different from placement parent)\n        if (sponsorId !== placementParentId) {\n            await updateCachedDownlineCounts(sponsorId);\n        }\n        // Update counts for the placement parent\n        await updateCachedDownlineCounts(placementParentId);\n        // Update counts for all upline users from the placement parent\n        const uplineUsers = await getUplineUsers(placementParentId);\n        const updatePromises = uplineUsers.map((user)=>updateCachedDownlineCounts(user.id));\n        await Promise.all(updatePromises);\n    } catch (error) {\n        console.error('Update tree counts after placement error:', error);\n    // Don't throw error as this is supplementary to placement\n    }\n}\n// Bulk update tree counts for multiple users (for maintenance)\nasync function bulkUpdateTreeCounts(userIds) {\n    try {\n        const updatePromises = userIds.map((userId)=>updateCachedDownlineCounts(userId));\n        await Promise.all(updatePromises);\n    } catch (error) {\n        console.error('Bulk update tree counts error:', error);\n    }\n}\n// Get tree health statistics\nasync function getTreeHealthStats(rootUserId) {\n    try {\n        const teamCounts = await getCachedDownlineCounts(rootUserId);\n        const totalUsers = teamCounts.total;\n        // Calculate balance ratio\n        const smallerSide = Math.min(teamCounts.left, teamCounts.right);\n        const largerSide = Math.max(teamCounts.left, teamCounts.right);\n        const balanceRatio = largerSide > 0 ? smallerSide / largerSide : 1;\n        // Calculate tree depth statistics\n        let maxDepth = 0;\n        let totalDepth = 0;\n        let userCount = 0;\n        // BFS to calculate depths\n        const queue = [\n            {\n                userId: rootUserId,\n                depth: 0\n            }\n        ];\n        const visited = new Set();\n        while(queue.length > 0){\n            const { userId, depth } = queue.shift();\n            if (visited.has(userId)) continue;\n            visited.add(userId);\n            maxDepth = Math.max(maxDepth, depth);\n            totalDepth += depth;\n            userCount++;\n            const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                where: {\n                    referrerId: userId\n                },\n                select: {\n                    referredId: true\n                }\n            });\n            for (const referral of referrals){\n                if (!visited.has(referral.referredId)) {\n                    queue.push({\n                        userId: referral.referredId,\n                        depth: depth + 1\n                    });\n                }\n            }\n        }\n        const averageDepth = userCount > 0 ? totalDepth / userCount : 0;\n        // Calculate empty positions (theoretical max - actual users)\n        const theoreticalMax = Math.pow(2, maxDepth + 1) - 1;\n        const emptyPositions = Math.max(0, theoreticalMax - totalUsers);\n        return {\n            totalUsers,\n            balanceRatio,\n            averageDepth,\n            maxDepth,\n            emptyPositions\n        };\n    } catch (error) {\n        console.error('Tree health stats error:', error);\n        return {\n            totalUsers: 0,\n            balanceRatio: 1,\n            averageDepth: 0,\n            maxDepth: 0,\n            emptyPositions: 0\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/referral.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freferrals%2Ftree%2Froute&page=%2Fapi%2Freferrals%2Ftree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freferrals%2Ftree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();