{"/_not-found/page": "app/_not-found/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/wallet/balance/route": "app/api/wallet/balance/route.js", "/api/earnings/route": "app/api/earnings/route.js", "/api/referrals/tree/route": "app/api/referrals/tree/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/mining-units/route": "app/api/mining-units/route.js", "/api/wallet/withdrawal-settings/route": "app/api/wallet/withdrawal-settings/route.js", "/api/admin/settings/pricing/route": "app/api/admin/settings/pricing/route.js", "/api/kyc/documents/route": "app/api/kyc/documents/route.js", "/api/wallet/transactions/route": "app/api/wallet/transactions/route.js", "/api/user/withdrawal-address/route": "app/api/user/withdrawal-address/route.js", "/api/support/tickets/route": "app/api/support/tickets/route.js", "/api/binary-points/info/route": "app/api/binary-points/info/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/admin/check/route": "app/api/admin/check/route.js", "/api/admin/kyc/all/route": "app/api/admin/kyc/all/route.js", "/api/admin/withdrawals/route": "app/api/admin/withdrawals/route.js", "/api/admin/deposits/route": "app/api/admin/deposits/route.js", "/api/admin/binary-points/route": "app/api/admin/binary-points/route.js", "/api/admin/support/tickets/route": "app/api/admin/support/tickets/route.js", "/api/admin/binary-points/history/route": "app/api/admin/binary-points/history/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/admin/referral-commissions/stats/route": "app/api/admin/referral-commissions/stats/route.js", "/api/admin/logs/route": "app/api/admin/logs/route.js", "/api/admin/referral-commissions/route": "app/api/admin/referral-commissions/route.js", "/api/admin/stats/route": "app/api/admin/stats/route.js", "/api/admin/email-settings/route": "app/api/admin/email-settings/route.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js", "/admin/page": "app/admin/page.js"}