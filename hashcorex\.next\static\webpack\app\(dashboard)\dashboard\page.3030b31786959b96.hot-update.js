"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/D3BinaryTree.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/D3BinaryTree.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D3BinaryTree: () => (/* binding */ D3BinaryTree)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Copy,Eye,EyeOff,RefreshCw,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Copy,Eye,EyeOff,RefreshCw,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Copy,Eye,EyeOff,RefreshCw,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Copy,Eye,EyeOff,RefreshCw,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Copy,Eye,EyeOff,RefreshCw,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Copy,Eye,EyeOff,RefreshCw,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Copy,Eye,EyeOff,RefreshCw,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Copy,Eye,EyeOff,RefreshCw,Share2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _BinaryPointsInfoPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BinaryPointsInfoPanel */ \"(app-pages-browser)/./src/components/dashboard/BinaryPointsInfoPanel.tsx\");\n/* harmony import */ var d3_hierarchy__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! d3-hierarchy */ \"(app-pages-browser)/./node_modules/d3-hierarchy/src/hierarchy/index.js\");\n/* harmony import */ var d3_hierarchy__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! d3-hierarchy */ \"(app-pages-browser)/./node_modules/d3-hierarchy/src/tree.js\");\n/* harmony import */ var d3_selection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! d3-selection */ \"(app-pages-browser)/./node_modules/d3-selection/src/select.js\");\n/* harmony import */ var d3_zoom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-zoom */ \"(app-pages-browser)/./node_modules/d3-zoom/src/index.js\");\n/* harmony import */ var d3_transition__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! d3-transition */ \"(app-pages-browser)/./node_modules/d3-transition/src/index.js\");\n/* __next_internal_client_entry_do_not_use__ D3BinaryTree auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst D3BinaryTree = ()=>{\n    _s();\n    const svgRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [treeData, setTreeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showInactive, setShowInactive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // D3 tree layout configuration\n    const margin = {\n        top: 50,\n        right: 120,\n        bottom: 50,\n        left: 120\n    };\n    const width = 1200 - margin.left - margin.right;\n    const height = 800 - margin.bottom - margin.top;\n    const nodeWidth = 200;\n    const nodeHeight = 120;\n    const fetchTreeData = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/referrals/tree?depth=20&enhanced=true&expanded=\".concat(Array.from(expandedNodes).join(',')), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setTreeData(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch binary tree data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Convert binary tree structure to D3 hierarchy format with expand/collapse support\n    const convertToD3Hierarchy = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"D3BinaryTree.useCallback[convertToD3Hierarchy]\": (node)=>{\n            const hierarchyNode = (0,d3_hierarchy__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(node, {\n                \"D3BinaryTree.useCallback[convertToD3Hierarchy].hierarchyNode\": (d)=>{\n                    // Check if this node should show its children\n                    const isExpanded = expandedNodes.has(d.user.id);\n                    // Root node (first level) is always expanded, others depend on expanded state\n                    const shouldShowChildren = d === node || isExpanded;\n                    if (!shouldShowChildren) {\n                        return null; // Don't show children if node is collapsed\n                    }\n                    const nodeChildren = [];\n                    // Add left child if it exists and should be visible\n                    if (d.leftChild && (showInactive || d.leftChild.user.isActive)) {\n                        nodeChildren.push(d.leftChild);\n                    }\n                    // Add right child if it exists and should be visible\n                    if (d.rightChild && (showInactive || d.rightChild.user.isActive)) {\n                        nodeChildren.push(d.rightChild);\n                    }\n                    return nodeChildren.length > 0 ? nodeChildren : null;\n                }\n            }[\"D3BinaryTree.useCallback[convertToD3Hierarchy].hierarchyNode\"]);\n            return hierarchyNode;\n        }\n    }[\"D3BinaryTree.useCallback[convertToD3Hierarchy]\"], [\n        showInactive,\n        expandedNodes\n    ]);\n    // Initialize and render the D3 tree\n    const renderTree = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"D3BinaryTree.useCallback[renderTree]\": ()=>{\n            if (!treeData || !svgRef.current) return;\n            try {\n                const svg = (0,d3_selection__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(svgRef.current);\n                svg.selectAll(\"*\").remove(); // Clear previous render\n                // Create main group for zoom/pan\n                const container = svg.attr(\"width\", width + margin.left + margin.right).attr(\"height\", height + margin.top + margin.bottom);\n                const g = container.append(\"g\").attr(\"class\", \"tree-container\").attr(\"transform\", \"translate(\".concat(margin.left, \",\").concat(margin.top, \")\"));\n                // Setup zoom behavior\n                const zoomBehavior = (0,d3_zoom__WEBPACK_IMPORTED_MODULE_5__.zoom)().scaleExtent([\n                    0.1,\n                    3\n                ]).on(\"zoom\", {\n                    \"D3BinaryTree.useCallback[renderTree].zoomBehavior\": (event)=>{\n                        g.attr(\"transform\", event.transform);\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree].zoomBehavior\"]);\n                svg.call(zoomBehavior);\n                // Create tree layout\n                const treeLayout = (0,d3_hierarchy__WEBPACK_IMPORTED_MODULE_9__[\"default\"])().size([\n                    width,\n                    height\n                ]).nodeSize([\n                    nodeWidth + 50,\n                    nodeHeight + 80\n                ]) // Add spacing between nodes\n                .separation({\n                    \"D3BinaryTree.useCallback[renderTree].treeLayout\": (a, b)=>{\n                        // Increase separation for better spacing\n                        return a.parent === b.parent ? 2 : 3;\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree].treeLayout\"]);\n                // Convert data to hierarchy and apply layout\n                const root = convertToD3Hierarchy(treeData.treeStructure);\n                const treeRoot = treeLayout(root);\n                // Center the tree\n                const nodes = treeRoot.descendants();\n                const links = treeRoot.links();\n                // Adjust positions to center the tree\n                let minX = Infinity;\n                let maxX = -Infinity;\n                nodes.forEach({\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>{\n                        if (d.x < minX) minX = d.x;\n                        if (d.x > maxX) maxX = d.x;\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                const centerOffset = width / 2 - (minX + maxX) / 2;\n                nodes.forEach({\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>{\n                        d.x += centerOffset;\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Render links (connections between nodes)\n                const linkGroup = g.append(\"g\").attr(\"class\", \"links\");\n                linkGroup.selectAll(\".link\").data(links).enter().append(\"path\").attr(\"class\", \"link\").attr(\"d\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>{\n                        const source = d.source;\n                        const target = d.target;\n                        // Create smooth curved path\n                        return \"M\".concat(source.x, \",\").concat(source.y + nodeHeight / 2, \"\\n                C\").concat(source.x, \",\").concat((source.y + target.y) / 2, \"\\n                 \").concat(target.x, \",\").concat((source.y + target.y) / 2, \"\\n                 \").concat(target.x, \",\").concat(target.y - nodeHeight / 2);\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree]\"]).style(\"fill\", \"none\").style(\"stroke\", \"#94a3b8\").style(\"stroke-width\", \"2px\").style(\"stroke-opacity\", 0.6);\n                // Render nodes\n                const nodeGroup = g.append(\"g\").attr(\"class\", \"nodes\");\n                const nodeEnter = nodeGroup.selectAll(\".node\").data(nodes).enter().append(\"g\").attr(\"class\", \"node\").attr(\"transform\", {\n                    \"D3BinaryTree.useCallback[renderTree].nodeEnter\": (d)=>\"translate(\".concat(d.x - nodeWidth / 2, \",\").concat(d.y - nodeHeight / 2, \")\")\n                }[\"D3BinaryTree.useCallback[renderTree].nodeEnter\"]).style(\"cursor\", \"pointer\");\n                // Add node background (card)\n                nodeEnter.append(\"rect\").attr(\"width\", nodeWidth).attr(\"height\", nodeHeight).attr(\"rx\", 12).attr(\"ry\", 12).style(\"fill\", \"white\").style(\"stroke\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>{\n                        // Different border colors based on state\n                        if (d.data.user.isActive) {\n                            return (d.data.hasLeftChild || d.data.hasRightChild) && !expandedNodes.has(d.data.user.id) ? \"#3b82f6\" // Blue border for collapsed nodes with children\n                             : \"#10b981\"; // Green border for active nodes\n                        }\n                        return \"#6b7280\"; // Gray border for inactive nodes\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree]\"]).style(\"stroke-width\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>{\n                        // Thicker border for nodes with collapsed children\n                        return (d.data.hasLeftChild || d.data.hasRightChild) && !expandedNodes.has(d.data.user.id) ? 3 : 2;\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree]\"]).style(\"filter\", \"drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))\");\n                // Add avatar circle background\n                nodeEnter.append(\"circle\").attr(\"cx\", nodeWidth / 2).attr(\"cy\", 25).attr(\"r\", 18).style(\"fill\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>d.data.user.isActive ? \"#10b981\" : \"#6b7280\"\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Add profile picture or initials\n                nodeEnter.each({\n                    \"D3BinaryTree.useCallback[renderTree]\": function(d) {\n                        const node = (0,d3_selection__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this);\n                        const user = d.data.user;\n                        if (user.profilePicture) {\n                            // Add profile picture\n                            node.append(\"defs\").append(\"pattern\").attr(\"id\", \"profile-\".concat(user.id)).attr(\"patternUnits\", \"objectBoundingBox\").attr(\"width\", 1).attr(\"height\", 1).append(\"image\").attr(\"href\", user.profilePicture).attr(\"width\", 36).attr(\"height\", 36).attr(\"preserveAspectRatio\", \"xMidYMid slice\");\n                            node.append(\"circle\").attr(\"cx\", nodeWidth / 2).attr(\"cy\", 25).attr(\"r\", 17).style(\"fill\", \"url(#profile-\".concat(user.id, \")\")).style(\"stroke\", user.isActive ? \"#10b981\" : \"#6b7280\").style(\"stroke-width\", 2);\n                        } else {\n                            // Add initials text\n                            node.append(\"text\").attr(\"x\", nodeWidth / 2).attr(\"y\", 25).attr(\"dy\", \"0.35em\").style(\"text-anchor\", \"middle\").style(\"fill\", \"white\").style(\"font-weight\", \"bold\").style(\"font-size\", \"12px\").text({\n                                \"D3BinaryTree.useCallback[renderTree]\": ()=>{\n                                    const firstName = user.firstName || '';\n                                    const lastName = user.lastName || '';\n                                    return \"\".concat(firstName[0] || '').concat(lastName[0] || '').toUpperCase();\n                                }\n                            }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                        }\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Add name text\n                nodeEnter.append(\"text\").attr(\"x\", nodeWidth / 2).attr(\"y\", 55).style(\"text-anchor\", \"middle\").style(\"font-weight\", \"600\").style(\"font-size\", \"13px\").style(\"fill\", \"#1f2937\").text({\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>\"\".concat(d.data.user.firstName, \" \").concat(d.data.user.lastName)\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Add status badge\n                nodeEnter.append(\"rect\").attr(\"x\", nodeWidth / 2 - 25).attr(\"y\", 65).attr(\"width\", 50).attr(\"height\", 16).attr(\"rx\", 8).style(\"fill\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>d.data.user.isActive ? \"#dcfce7\" : \"#f3f4f6\"\n                }[\"D3BinaryTree.useCallback[renderTree]\"]).style(\"stroke\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>d.data.user.isActive ? \"#16a34a\" : \"#6b7280\"\n                }[\"D3BinaryTree.useCallback[renderTree]\"]).style(\"stroke-width\", 1);\n                nodeEnter.append(\"text\").attr(\"x\", nodeWidth / 2).attr(\"y\", 73).attr(\"dy\", \"0.35em\").style(\"text-anchor\", \"middle\").style(\"font-size\", \"10px\").style(\"font-weight\", \"500\").style(\"fill\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>d.data.user.isActive ? \"#16a34a\" : \"#6b7280\"\n                }[\"D3BinaryTree.useCallback[renderTree]\"]).text({\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>d.data.user.isActive ? \"Active\" : \"Inactive\"\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Add sponsor info if available\n                nodeEnter.filter({\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>d.data.sponsorInfo\n                }[\"D3BinaryTree.useCallback[renderTree]\"]).append(\"text\").attr(\"x\", nodeWidth / 2).attr(\"y\", 90).style(\"text-anchor\", \"middle\").style(\"font-size\", \"9px\").style(\"fill\", \"#3b82f6\").text({\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>\"Sponsor: \".concat(d.data.sponsorInfo.firstName, \" \").concat(d.data.sponsorInfo.lastName)\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Add team counts\n                nodeEnter.append(\"text\").attr(\"x\", nodeWidth / 2).attr(\"y\", 105).style(\"text-anchor\", \"middle\").style(\"font-size\", \"10px\").style(\"font-weight\", \"500\").style(\"fill\", \"#4b5563\").text({\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>\"Team: \".concat(d.data.teamCounts.total, \" (L:\").concat(d.data.teamCounts.left, \" R:\").concat(d.data.teamCounts.right, \")\")\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Add expand/collapse functionality for nodes with children\n                const expandableNodes = nodeEnter.filter({\n                    \"D3BinaryTree.useCallback[renderTree].expandableNodes\": (d)=>d.data.hasLeftChild || d.data.hasRightChild\n                }[\"D3BinaryTree.useCallback[renderTree].expandableNodes\"]);\n                // Add expand/collapse button background\n                expandableNodes.append(\"circle\").attr(\"cx\", nodeWidth - 15).attr(\"cy\", 15).attr(\"r\", 10).style(\"fill\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>expandedNodes.has(d.data.user.id) ? \"#ef4444\" : \"#3b82f6\"\n                }[\"D3BinaryTree.useCallback[renderTree]\"]).style(\"stroke\", \"white\").style(\"stroke-width\", 2).style(\"cursor\", \"pointer\").style(\"transition\", \"all 0.2s ease\").style(\"transform-origin\", \"center\").on(\"click\", {\n                    \"D3BinaryTree.useCallback[renderTree]\": function(event, d) {\n                        event.stopPropagation();\n                        handleNodeToggle(d.data.user.id);\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Add expand/collapse button text\n                expandableNodes.append(\"text\").attr(\"x\", nodeWidth - 15).attr(\"y\", 15).attr(\"dy\", \"0.35em\").style(\"text-anchor\", \"middle\").style(\"font-size\", \"12px\").style(\"font-weight\", \"bold\").style(\"fill\", \"white\").style(\"cursor\", \"pointer\").style(\"pointer-events\", \"none\") // Let clicks pass through to circle\n                .text({\n                    \"D3BinaryTree.useCallback[renderTree]\": (d)=>expandedNodes.has(d.data.user.id) ? \"−\" : \"+\"\n                }[\"D3BinaryTree.useCallback[renderTree]\"]);\n                // Add zoom controls\n                const controls = container.append(\"g\").attr(\"class\", \"zoom-controls\").attr(\"transform\", \"translate(\".concat(width + margin.left - 100, \", 20)\"));\n                // Zoom in button\n                const zoomInBtn = controls.append(\"g\").style(\"cursor\", \"pointer\").on(\"click\", {\n                    \"D3BinaryTree.useCallback[renderTree].zoomInBtn\": ()=>{\n                        svg.transition().duration(300).call(zoomBehavior.scaleBy, 1.5);\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree].zoomInBtn\"]);\n                zoomInBtn.append(\"rect\").attr(\"width\", 30).attr(\"height\", 30).attr(\"rx\", 4).style(\"fill\", \"white\").style(\"stroke\", \"#d1d5db\").style(\"stroke-width\", 1);\n                zoomInBtn.append(\"text\").attr(\"x\", 15).attr(\"y\", 15).attr(\"dy\", \"0.35em\").style(\"text-anchor\", \"middle\").style(\"font-size\", \"16px\").style(\"font-weight\", \"bold\").text(\"+\");\n                // Zoom out button\n                const zoomOutBtn = controls.append(\"g\").attr(\"transform\", \"translate(0, 35)\").style(\"cursor\", \"pointer\").on(\"click\", {\n                    \"D3BinaryTree.useCallback[renderTree].zoomOutBtn\": ()=>{\n                        svg.transition().duration(300).call(zoomBehavior.scaleBy, 0.67);\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree].zoomOutBtn\"]);\n                zoomOutBtn.append(\"rect\").attr(\"width\", 30).attr(\"height\", 30).attr(\"rx\", 4).style(\"fill\", \"white\").style(\"stroke\", \"#d1d5db\").style(\"stroke-width\", 1);\n                zoomOutBtn.append(\"text\").attr(\"x\", 15).attr(\"y\", 15).attr(\"dy\", \"0.35em\").style(\"text-anchor\", \"middle\").style(\"font-size\", \"16px\").style(\"font-weight\", \"bold\").text(\"−\");\n                // Reset zoom button\n                const resetBtn = controls.append(\"g\").attr(\"transform\", \"translate(0, 70)\").style(\"cursor\", \"pointer\").on(\"click\", {\n                    \"D3BinaryTree.useCallback[renderTree].resetBtn\": ()=>{\n                        svg.transition().duration(500).call(zoomBehavior.transform, d3_zoom__WEBPACK_IMPORTED_MODULE_5__.zoomIdentity);\n                    }\n                }[\"D3BinaryTree.useCallback[renderTree].resetBtn\"]);\n                resetBtn.append(\"rect\").attr(\"width\", 30).attr(\"height\", 30).attr(\"rx\", 4).style(\"fill\", \"white\").style(\"stroke\", \"#d1d5db\").style(\"stroke-width\", 1);\n                resetBtn.append(\"text\").attr(\"x\", 15).attr(\"y\", 15).attr(\"dy\", \"0.35em\").style(\"text-anchor\", \"middle\").style(\"font-size\", \"12px\").style(\"font-weight\", \"bold\").text(\"⌂\");\n            } catch (error) {\n                console.error('Error rendering D3 tree:', error);\n            }\n        }\n    }[\"D3BinaryTree.useCallback[renderTree]\"], [\n        treeData,\n        expandedNodes,\n        showInactive,\n        convertToD3Hierarchy\n    ]);\n    const handleNodeToggle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"D3BinaryTree.useCallback[handleNodeToggle]\": (nodeId)=>{\n            setExpandedNodes({\n                \"D3BinaryTree.useCallback[handleNodeToggle]\": (prev)=>{\n                    const newSet = new Set(prev);\n                    if (newSet.has(nodeId)) {\n                        newSet.delete(nodeId);\n                    } else {\n                        newSet.add(nodeId);\n                    }\n                    return newSet;\n                }\n            }[\"D3BinaryTree.useCallback[handleNodeToggle]\"]);\n        }\n    }[\"D3BinaryTree.useCallback[handleNodeToggle]\"], []);\n    const handleCopyLink = async (link)=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.copyToClipboard)(link);\n        } catch (error) {\n            console.error('Failed to copy link:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"D3BinaryTree.useEffect\": ()=>{\n            fetchTreeData();\n        }\n    }[\"D3BinaryTree.useEffect\"], [\n        expandedNodes\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"D3BinaryTree.useEffect\": ()=>{\n            if (treeData) {\n                renderTree();\n            }\n        }\n    }[\"D3BinaryTree.useEffect\"], [\n        treeData,\n        renderTree,\n        expandedNodes\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"w-8 h-8 animate-spin text-blue-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                lineNumber: 525,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n            lineNumber: 524,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!treeData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                    lineNumber: 533,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                    children: \"No Tree Data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                    lineNumber: 534,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"Unable to load your binary tree structure.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n            lineNumber: 532,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Direct Referrals\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: treeData.statistics.totalDirectReferrals\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Left Side\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold text-green-600\",\n                                                children: [\n                                                    treeData.statistics.leftReferrals || 0,\n                                                    \" users\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-500\",\n                                                children: [\n                                                    treeData.statistics.binaryPoints.leftPoints,\n                                                    \" points\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Right Side\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold text-orange-600\",\n                                                children: [\n                                                    treeData.statistics.rightReferrals || 0,\n                                                    \" users\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-orange-500\",\n                                                children: [\n                                                    treeData.statistics.binaryPoints.rightPoints,\n                                                    \" points\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Binary Points\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold text-purple-600\",\n                                                children: [\n                                                    treeData.statistics.binaryPoints.matchedPoints,\n                                                    \" matched\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-purple-500\",\n                                                children: [\n                                                    Math.min(treeData.statistics.binaryPoints.leftPoints, treeData.statistics.binaryPoints.rightPoints),\n                                                    \" available\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                lineNumber: 543,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Referral Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"General Referral Link\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: treeData.referralLinks.general,\n                                                        readOnly: true,\n                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleCopyLink(treeData.referralLinks.general),\n                                                        className: \"rounded-l-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Left Side Link\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: treeData.referralLinks.left,\n                                                        readOnly: true,\n                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleCopyLink(treeData.referralLinks.left),\n                                                        className: \"rounded-l-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Right Side Link\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: treeData.referralLinks.right,\n                                                        readOnly: true,\n                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleCopyLink(treeData.referralLinks.right),\n                                                        className: \"rounded-l-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Enhanced Placement:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \" New users are automatically placed in your weaker leg for optimal network balance. Use specific side links to target placement.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Binary Matching:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \" Points are matched weekly on Saturdays at 15:00 UTC. Each $100 investment by your downline = 1 binary point. Maximum 100 points per side.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                        lineNumber: 606,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                lineNumber: 599,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Binary Tree Structure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowInactive(!showInactive),\n                                            children: [\n                                                showInactive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 33\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 66\n                                                }, undefined),\n                                                showInactive ? 'Hide Inactive' : 'Show Inactive'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: fetchTreeData,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Copy_Eye_EyeOff_RefreshCw_Share2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-200 rounded-lg overflow-hidden bg-gradient-to-br from-slate-50 to-blue-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    ref: svgRef,\n                                    className: \"w-full\",\n                                    style: {\n                                        height: '700px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                lineNumber: 705,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-sm text-gray-600 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"• \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Zoom:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 18\n                                            }, undefined),\n                                            \" Use mouse wheel or zoom controls\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"• \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pan:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 18\n                                            }, undefined),\n                                            \" Click and drag to move around\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"• \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Expand/Collapse:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 18\n                                            }, undefined),\n                                            \" Click the + or - button on nodes\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                lineNumber: 684,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BinaryPointsInfoPanel__WEBPACK_IMPORTED_MODULE_4__.BinaryPointsInfoPanel, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n                lineNumber: 723,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\D3BinaryTree.tsx\",\n        lineNumber: 541,\n        columnNumber: 5\n    }, undefined);\n};\n_s(D3BinaryTree, \"CK++SLLyIwR1hn0ujUmaSeaAwrY=\");\n_c = D3BinaryTree;\nvar _c;\n$RefreshReg$(_c, \"D3BinaryTree\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/D3BinaryTree.tsx\n"));

/***/ })

});