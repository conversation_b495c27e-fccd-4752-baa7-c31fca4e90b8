"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/UserProfileSettings.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProfileSettings: () => (/* binding */ UserProfileSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* __next_internal_client_entry_do_not_use__ UserProfileSettings auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst UserProfileSettings = ()=>{\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: true,\n        smsNotifications: false,\n        marketingEmails: false\n    });\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        referralId: ''\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordForm, setPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [withdrawalAddress, setWithdrawalAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Message box hook\n    const { showMessage, MessageBoxComponent } = (0,_components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserProfileSettings.useEffect\": ()=>{\n            if (user) {\n                setProfileData({\n                    firstName: user.firstName || '',\n                    lastName: user.lastName || '',\n                    email: user.email || '',\n                    referralId: user.referralId || ''\n                });\n            }\n            fetchNotificationSettings();\n            fetchUserData();\n        }\n    }[\"UserProfileSettings.useEffect\"], [\n        user\n    ]);\n    const fetchNotificationSettings = async ()=>{\n        try {\n            const response = await fetch('/api/user/notification-settings', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setNotifications(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch notification settings:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch('/api/user/withdrawal-address', {\n                credentials: 'include'\n            });\n            const data = await response.json();\n            if (data.success) {\n                setWithdrawalAddress(data.data.withdrawalAddress || '');\n            }\n        } catch (error) {\n            console.error('Failed to fetch user data:', error);\n        }\n    };\n    const updateNotificationSettings = async (settings)=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/user/notification-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(settings)\n            });\n            if (response.ok) {\n                setNotifications(settings);\n            }\n        } catch (error) {\n            console.error('Failed to update notification settings:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const updateProfile = async ()=>{\n        try {\n            setSaving(true);\n            // Profile update API call would go here\n            console.log('Profile update:', profileData);\n        } catch (error) {\n            console.error('Failed to update profile:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const updatePassword = async ()=>{\n        if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n            showMessage({\n                title: 'Password Mismatch',\n                message: 'New passwords do not match',\n                variant: 'error'\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            // Password update API call would go here\n            console.log('Password update');\n            setPasswordForm({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Failed to update password:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, undefined);\n    }\n    const tabs = [\n        {\n            id: 'profile',\n            label: 'Profile Information',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security Settings',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notification Settings',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'billing',\n            label: 'Billing & Payments',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ];\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'profile':\n                return renderProfileContent();\n            case 'security':\n                return renderSecurityContent();\n            case 'notifications':\n                return renderNotificationContent();\n            case 'billing':\n                return renderBillingContent();\n            default:\n                return renderProfileContent();\n        }\n    };\n    const renderProfileContent = ()=>{\n        const isKycSubmittedOrApproved = (user === null || user === void 0 ? void 0 : user.kycStatus) === 'PENDING' || (user === null || user === void 0 ? void 0 : user.kycStatus) === 'APPROVED';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Profile Information\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 font-medium\",\n                                            children: \"Profile names cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-1\",\n                                    children: \"Your identity has been verified and profile information is now locked for security.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"First Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"text\",\n                                            value: profileData.firstName,\n                                            onChange: (e)=>setProfileData((prev)=>({\n                                                        ...prev,\n                                                        firstName: e.target.value\n                                                    })),\n                                            disabled: isKycSubmittedOrApproved,\n                                            className: isKycSubmittedOrApproved ? \"bg-gray-100 border-gray-300 text-gray-500\" : \"bg-white border-gray-300 text-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Last Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"text\",\n                                            value: profileData.lastName,\n                                            onChange: (e)=>setProfileData((prev)=>({\n                                                        ...prev,\n                                                        lastName: e.target.value\n                                                    })),\n                                            disabled: isKycSubmittedOrApproved,\n                                            className: isKycSubmittedOrApproved ? \"bg-gray-100 border-gray-300 text-gray-500\" : \"bg-white border-gray-300 text-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"email\",\n                                    value: profileData.email,\n                                    disabled: true,\n                                    className: \"bg-gray-100 border-gray-300 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Email cannot be changed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Referral ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"text\",\n                                    value: profileData.referralId,\n                                    disabled: true,\n                                    className: \"bg-gray-100 border-gray-300 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Your unique referral identifier\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updateProfile,\n                            disabled: saving || isKycSubmittedOrApproved,\n                            className: \"w-full bg-yellow-500 hover:bg-yellow-600 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, undefined),\n                                saving ? 'Saving...' : isKycSubmittedOrApproved ? 'Profile Locked' : 'Save Profile'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderSecurityContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Security Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Current Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            value: passwordForm.currentPassword,\n                                            onChange: (e)=>setPasswordForm((prev)=>({\n                                                        ...prev,\n                                                        currentPassword: e.target.value\n                                                    })),\n                                            className: \"bg-white border-gray-300 text-gray-900 pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: passwordForm.newPassword,\n                                    onChange: (e)=>setPasswordForm((prev)=>({\n                                                ...prev,\n                                                newPassword: e.target.value\n                                            })),\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: passwordForm.confirmPassword,\n                                    onChange: (e)=>setPasswordForm((prev)=>({\n                                                ...prev,\n                                                confirmPassword: e.target.value\n                                            })),\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updatePassword,\n                            disabled: saving || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword,\n                            className: \"w-full bg-red-500 hover:bg-red-600 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 11\n                                }, undefined),\n                                saving ? 'Updating...' : 'Update Password'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 302,\n            columnNumber: 5\n        }, undefined);\n    const renderNotificationContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Notification Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Email Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Receive updates via email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.emailNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    emailNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Push Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Browser notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.pushNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    pushNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"SMS Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Text message alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.smsNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    smsNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 369,\n            columnNumber: 5\n        }, undefined);\n    const updateWithdrawalAddress = async ()=>{\n        try {\n            setSaving(true);\n            // Validate USDT TRC20 address format\n            if (withdrawalAddress && !withdrawalAddress.match(/^T[A-Za-z1-9]{33}$/)) {\n                showMessage({\n                    title: 'Invalid Address',\n                    message: 'Invalid USDT TRC20 address format. Address must start with \"T\" and be 34 characters long.',\n                    variant: 'error'\n                });\n                return;\n            }\n            const response = await fetch('/api/user/withdrawal-address', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    withdrawalAddress: withdrawalAddress.trim()\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Response not OK:', response.status, errorText);\n                throw new Error(\"Server error: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update withdrawal address');\n            }\n            // Show success message\n            showMessage({\n                title: 'Success',\n                message: 'Withdrawal address updated successfully!',\n                variant: 'success'\n            });\n        } catch (error) {\n            console.error('Failed to update withdrawal address:', error);\n            showMessage({\n                title: 'Error',\n                message: \"Error: \".concat(error.message),\n                variant: 'error'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const renderBillingContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Billing & Payments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 504,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Default Withdrawal Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"text\",\n                                    value: withdrawalAddress,\n                                    onChange: (e)=>setWithdrawalAddress(e.target.value),\n                                    placeholder: \"Enter your USDT TRC20 address\",\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"This address will be automatically filled in withdrawal forms. Only USDT TRC20 addresses are supported.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-800 mb-1\",\n                                                children: \"Security Notice\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: \"Your withdrawal address is encrypted and stored securely. You can update it anytime, but make sure to double-check the address as transactions cannot be reversed.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updateWithdrawalAddress,\n                            disabled: saving,\n                            className: \"w-full bg-yellow-500 hover:bg-yellow-600 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 11\n                                }, undefined),\n                                saving ? 'Saving...' : 'Save Withdrawal Address'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 503,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Profile & Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Manage your account information and preferences\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 555,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: tabs.map((tab)=>{\n                        const Icon = tab.icon;\n                        const isActive = activeTab === tab.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"\\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\\n                  \".concat(isActive ? 'border-yellow-500 text-yellow-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \"\\n                \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: renderTabContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 588,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBoxComponent, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 593,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n        lineNumber: 553,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UserProfileSettings, \"jaPUFdCLqv6vrjZ9kOgy4KUb48k=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox\n    ];\n});\n_c = UserProfileSettings;\nvar _c;\n$RefreshReg$(_c, \"UserProfileSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx\n"));

/***/ })

});