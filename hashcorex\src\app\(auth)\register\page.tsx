'use client';

import React, { useState, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button, Input } from '@/components/ui';
import { SolarPanel } from '@/components/icons';
import { Eye, EyeOff, ArrowLeft, Check } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { validatePassword } from '@/lib/utils';
import { GuestRoute } from '@/components/auth/AuthGuard';
import { OTPVerification } from '@/components/auth/OTPVerification';

function RegisterForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { register, loading } = useAuth();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    referralCode: searchParams.get('ref') || '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [passwordValidation, setPasswordValidation] = useState({
    isValid: false,
    errors: [],
  });
  const [step, setStep] = useState<'form' | 'otp'>('form');
  const [otpSent, setOtpSent] = useState(false);
  const [sendingOtp, setSendingOtp] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (!passwordValidation.isValid) {
      setError('Please ensure your password meets all requirements');
      return;
    }

    // Send OTP for email verification
    await sendOTP();
  };

  const sendOTP = async () => {
    try {
      setSendingOtp(true);
      setError('');

      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          firstName: formData.firstName,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setOtpSent(true);
        setStep('otp');
      } else {
        setError(data.error || 'Failed to send OTP');
      }
    } catch (error) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setSendingOtp(false);
    }
  };

  const handleOTPVerified = async (otp: string) => {
    try {
      setError('');

      // Get side parameter from URL
      const side = searchParams.get('side') as 'left' | 'right' | null;

      await register(
        formData.email,
        formData.firstName,
        formData.lastName,
        formData.password,
        formData.referralCode,
        side || undefined,
        otp
      );

      // Check for redirect parameter
      const redirectTo = searchParams.get('redirect');
      if (redirectTo && redirectTo.startsWith('/')) {
        router.push(redirectTo);
      } else {
        router.push('/dashboard');
      }
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Registration failed');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Validate password in real-time
    if (name === 'password') {
      setPasswordValidation(validatePassword(value));
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="absolute inset-0 animated-gradient opacity-30"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-white/95 via-solar-50/90 to-eco-50/95"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-16 h-16 bg-solar-400/20 rounded-full animate-float"></div>
      <div className="absolute top-40 right-20 w-12 h-12 bg-eco-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-40 left-20 w-10 h-10 bg-purple-400/20 rounded-full animate-float" style={{animationDelay: '4s'}}></div>

      {/* Responsive Layout */}
      <div className="relative z-10 min-h-screen flex">
        {/* Left Side - Hidden on mobile, visible on desktop */}
        <div className="hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-solar-500/10 to-eco-500/10 items-center justify-center p-12">
          <div className="max-w-lg text-center">
            <div className="mb-8">
              <div className="relative inline-block">
                <div className="absolute inset-0 bg-solar-500/20 rounded-full animate-ping"></div>
                <SolarPanel className="relative h-20 w-20 text-solar-500" />
              </div>
            </div>
            <h1 className="text-4xl xl:text-5xl font-black text-dark-900 mb-6">
              Join the Future of{' '}
              <span className="bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent">
                Sustainable Mining
              </span>
            </h1>
            <p className="text-lg text-gray-600 leading-relaxed mb-8">
              Start your journey with HashCoreX and be part of the green revolution in cryptocurrency mining.
            </p>
            <div className="grid grid-cols-2 gap-6 text-left">
              <div className="bg-white/50 rounded-lg p-4">
                <div className="text-2xl font-bold text-solar-600 mb-1">100%</div>
                <div className="text-sm text-gray-600">Renewable Energy</div>
              </div>
              <div className="bg-white/50 rounded-lg p-4">
                <div className="text-2xl font-bold text-eco-600 mb-1">24/7</div>
                <div className="text-sm text-gray-600">Mining Operations</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Form */}
        <div className="w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-6 lg:p-12">
          <div className="w-full max-w-md">
            <div className="text-center mb-8">
              <Link href="/" className="inline-flex items-center space-x-3 group mb-8">
                <div className="relative">
                  <SolarPanel className="h-10 w-10 lg:h-12 lg:w-12 text-solar-500 group-hover:scale-110 transition-transform" />
                  <div className="absolute inset-0 bg-solar-500/20 rounded-full animate-ping"></div>
                </div>
                <span className="text-2xl lg:text-4xl font-black bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent">
                  HashCoreX
                </span>
              </Link>
              <h1 className="text-2xl lg:text-3xl xl:text-4xl font-black text-dark-900 mb-3 lg:mb-4">Create Account</h1>
              <p className="text-base lg:text-lg text-gray-600 font-medium">Start your sustainable mining journey</p>
            </div>

            {/* Form or OTP Verification */}
            {step === 'form' ? (
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
                    {error}
                  </div>
                )}

                {/* Name Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-semibold text-gray-700 mb-2">
                      First Name
                    </label>
                    <Input
                      id="firstName"
                      name="firstName"
                      type="text"
                      required
                      value={formData.firstName}
                      onChange={handleChange}
                      className="w-full"
                      placeholder="Enter your first name"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-semibold text-gray-700 mb-2">
                      Last Name
                    </label>
                    <Input
                      id="lastName"
                      name="lastName"
                      type="text"
                      required
                      value={formData.lastName}
                      onChange={handleChange}
                      className="w-full"
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>

                {/* Email Field */}
                <div>
                  <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                    Email Address
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full"
                    placeholder="Enter your email address"
                  />
                </div>

                {/* Password Field */}
                <div>
                  <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      required
                      value={formData.password}
                      onChange={handleChange}
                      className="w-full pr-12"
                      placeholder="Create a strong password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>

                  {/* Password Validation */}
                  {formData.password && (
                    <div className="mt-2 space-y-1">
                      {passwordValidation.errors.map((error, index) => (
                        <div key={index} className="flex items-center text-sm">
                          <Check className={`h-4 w-4 mr-2 ${passwordValidation.isValid ? 'text-green-500' : 'text-red-500'}`} />
                          <span className={passwordValidation.isValid ? 'text-green-600' : 'text-red-600'}>
                            {error}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Confirm Password Field */}
                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-700 mb-2">
                    Confirm Password
                  </label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      required
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="w-full pr-12"
                      placeholder="Confirm your password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>

                  {/* Password Match Validation */}
                  {formData.confirmPassword && (
                    <div className="mt-2">
                      <div className="flex items-center text-sm">
                        <Check className={`h-4 w-4 mr-2 ${formData.password === formData.confirmPassword ? 'text-green-500' : 'text-red-500'}`} />
                        <span className={formData.password === formData.confirmPassword ? 'text-green-600' : 'text-red-600'}>
                          {formData.password === formData.confirmPassword ? 'Passwords match' : 'Passwords do not match'}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Referral Code Field */}
                <div>
                  <label htmlFor="referralCode" className="block text-sm font-semibold text-gray-700 mb-2">
                    Referral Code (Optional)
                  </label>
                  <Input
                    id="referralCode"
                    name="referralCode"
                    type="text"
                    value={formData.referralCode}
                    onChange={handleChange}
                    className="w-full"
                    placeholder="Enter referral code if you have one"
                  />
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  variant="primary"
                  size="xl"
                  className="w-full font-bold"
                  loading={sendingOtp}
                  disabled={!passwordValidation.isValid || formData.password !== formData.confirmPassword || sendingOtp}
                >
                  {sendingOtp ? 'Sending OTP...' : 'Send Verification Code'}
                </Button>
              </form>
            ) : (
              <div className="space-y-6">
                <OTPVerification
                  email={formData.email}
                  firstName={formData.firstName}
                  onVerified={handleOTPVerified}
                  onResend={sendOTP}
                  loading={loading}
                  error={error}
                />
              </div>
            )}

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-gray-600">
                Already have an account?{' '}
                <Link href="/login" className="text-solar-500 hover:text-solar-600 font-medium">
                  Sign in
                </Link>
              </p>
            </div>

            {/* Back to Home */}
            <div className="mt-6 text-center lg:hidden">
              <Link href="/" className="inline-flex items-center text-gray-500 hover:text-gray-700 text-sm">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function RegisterPage() {
  return (
    <GuestRoute redirectTo="/dashboard">
      <Suspense fallback={
        <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      }>
        <RegisterForm />
      </Suspense>
    </GuestRoute>
  );
}
