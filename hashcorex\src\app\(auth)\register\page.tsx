'use client';

import React, { useState, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button, Input } from '@/components/ui';
// import { Container, Flex } from '@/components/layout';
import { SolarPanel } from '@/components/icons';
import { Eye, EyeOff, ArrowLeft, Check } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { validatePassword } from '@/lib/utils';

function RegisterForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { register, loading } = useAuth();
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    referralCode: searchParams.get('ref') || '',
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [passwordValidation, setPasswordValidation] = useState({
    isValid: false,
    errors: [],
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    // Validate password strength
    if (!passwordValidation.isValid) {
      setError('Please ensure your password meets all requirements');
      return;
    }

    try {
      // Get side parameter from URL
      const side = searchParams.get('side') as 'left' | 'right' | null;

      await register(
        formData.email,
        formData.firstName,
        formData.lastName,
        formData.password,
        formData.confirmPassword,
        formData.referralCode || undefined,
        side || undefined
      );
      router.push('/dashboard');
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Registration failed');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Validate password in real-time
    if (name === 'password') {
      setPasswordValidation(validatePassword(value));
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="absolute inset-0 animated-gradient opacity-30"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-white/95 via-solar-50/90 to-eco-50/95"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-16 h-16 bg-solar-400/20 rounded-full animate-float"></div>
      <div className="absolute top-40 right-20 w-12 h-12 bg-eco-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-40 left-20 w-10 h-10 bg-purple-400/20 rounded-full animate-float" style={{animationDelay: '4s'}}></div>

      {/* Responsive Layout */}
      <div className="relative z-10 min-h-screen flex">
        {/* Left Side - Hidden on mobile, visible on desktop */}
        <div className="hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-solar-500/10 to-eco-500/10 items-center justify-center p-12">
          <div className="max-w-lg text-center">
            <div className="mb-8">
              <SolarPanel className="h-24 w-24 text-solar-500 mx-auto mb-6" />
              <h2 className="text-4xl xl:text-5xl font-black text-dark-900 mb-4">
                Start Your Mining Journey
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                Join our sustainable mining revolution and start earning daily returns
                with our eco-friendly, solar-powered platform.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-6 text-left">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-eco-500/20 rounded-full flex items-center justify-center">
                  <span className="text-eco-600 font-bold">🌱</span>
                </div>
                <div>
                  <h3 className="font-semibold text-dark-900">Sustainable Mining</h3>
                  <p className="text-gray-600">Powered by renewable solar energy</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-solar-500/20 rounded-full flex items-center justify-center">
                  <span className="text-solar-600 font-bold">💰</span>
                </div>
                <div>
                  <h3 className="font-semibold text-dark-900">Binary Referral System</h3>
                  <p className="text-gray-600">Earn from direct referrals & binary matching</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 font-bold">🚀</span>
                </div>
                <div>
                  <h3 className="font-semibold text-dark-900">Easy Start</h3>
                  <p className="text-gray-600">Get started with just $50 minimum</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Form */}
        <div className="w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-4 lg:p-8">
          <div className="w-full max-w-md">
            <div className="glass-morphism rounded-3xl p-8 lg:p-10 shadow-2xl">
              {/* Premium Header */}
              <div className="text-center mb-8 lg:mb-10">
                <Link href="/" className="inline-flex items-center space-x-3 mb-6 lg:mb-8 group lg:hidden">
                  <div className="relative">
                    <SolarPanel className="h-10 w-10 lg:h-12 lg:w-12 text-solar-500 group-hover:scale-110 transition-transform" />
                    <div className="absolute inset-0 bg-solar-500/20 rounded-full animate-ping"></div>
                  </div>
                  <span className="text-2xl lg:text-4xl font-black bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent">
                    HashCoreX
                  </span>
                </Link>
                <h1 className="text-2xl lg:text-3xl xl:text-4xl font-black text-dark-900 mb-3 lg:mb-4">Create Account</h1>
                <p className="text-base lg:text-lg text-gray-600 font-medium">Start your sustainable mining journey</p>
              </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Input
                label="First Name"
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                placeholder="Enter your first name"
                required
              />

              <Input
                label="Last Name"
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                placeholder="Enter your last name"
                required
              />
            </div>

            <Input
              label="Email Address"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              required
            />

            <Input
              label="Referral Code (Optional)"
              type="text"
              name="referralCode"
              value={formData.referralCode}
              onChange={handleChange}
              placeholder="Enter referral code"
            />

            <Input
              label="Password"
              type={showPassword ? 'text' : 'password'}
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Create a strong password"
              required
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              }
            />

            {/* Password Requirements */}
            {formData.password && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Password Requirements:</h4>
                <div className="space-y-1">
                  {[
                    { text: 'At least 8 characters', check: formData.password.length >= 8 },
                    { text: 'One uppercase letter', check: /[A-Z]/.test(formData.password) },
                    { text: 'One lowercase letter', check: /[a-z]/.test(formData.password) },
                    { text: 'One number', check: /\d/.test(formData.password) },
                    { text: 'One special character', check: /[!@#$%^&*(),.?":{}|<>]/.test(formData.password) },
                  ].map((req, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Check 
                        className={`h-4 w-4 ${req.check ? 'text-eco-500' : 'text-gray-300'}`} 
                      />
                      <span className={`text-sm ${req.check ? 'text-eco-600' : 'text-gray-500'}`}>
                        {req.text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <Input
              label="Confirm Password"
              type={showConfirmPassword ? 'text' : 'password'}
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="Confirm your password"
              required
              error={
                formData.confirmPassword && formData.password !== formData.confirmPassword
                  ? 'Passwords do not match'
                  : undefined
              }
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              }
            />

            <div className="flex items-start space-x-2">
              <input
                type="checkbox"
                required
                className="mt-1 rounded border-gray-300 text-solar-500 focus:ring-solar-500"
              />
              <span className="text-sm text-gray-600">
                I agree to the{' '}
                <Link href="/terms" className="text-solar-500 hover:text-solar-600">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-solar-500 hover:text-solar-600">
                  Privacy Policy
                </Link>
              </span>
            </div>

            <Button
              type="submit"
              variant="primary"
              size="xl"
              className="w-full font-bold"
              loading={loading}
              disabled={!passwordValidation.isValid || formData.password !== formData.confirmPassword}
            >
              Create Account
            </Button>
          </form>

              {/* Footer */}
              <div className="mt-8 text-center">
                <p className="text-gray-600">
                  Already have an account?{' '}
                  <Link href="/login" className="text-solar-500 hover:text-solar-600 font-medium">
                    Sign in
                  </Link>
                </p>
              </div>

              {/* Back to Home */}
              <div className="mt-6 text-center lg:hidden">
                <Link href="/" className="inline-flex items-center text-gray-500 hover:text-gray-700 text-sm">
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back to Home
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function RegisterPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <RegisterForm />
    </Suspense>
  );
}
