"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/UserProfileSettings.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProfileSettings: () => (/* binding */ UserProfileSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Eye,EyeOff,Key,Mail,Save,Settings,Shield,Smartphone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* __next_internal_client_entry_do_not_use__ UserProfileSettings auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst UserProfileSettings = ()=>{\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: true,\n        smsNotifications: false,\n        marketingEmails: false\n    });\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        referralId: ''\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordForm, setPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [withdrawalAddress, setWithdrawalAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadingPicture, setUploadingPicture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Message box hook\n    const { showMessage, MessageBoxComponent } = (0,_components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserProfileSettings.useEffect\": ()=>{\n            if (user) {\n                setProfileData({\n                    firstName: user.firstName || '',\n                    lastName: user.lastName || '',\n                    email: user.email || '',\n                    referralId: user.referralId || ''\n                });\n            }\n            fetchNotificationSettings();\n            fetchUserData();\n        }\n    }[\"UserProfileSettings.useEffect\"], [\n        user\n    ]);\n    const fetchNotificationSettings = async ()=>{\n        try {\n            const response = await fetch('/api/user/notification-settings', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setNotifications(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch notification settings:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch('/api/user/withdrawal-address', {\n                credentials: 'include'\n            });\n            const data = await response.json();\n            if (data.success) {\n                setWithdrawalAddress(data.data.withdrawalAddress || '');\n            }\n        } catch (error) {\n            console.error('Failed to fetch user data:', error);\n        }\n    };\n    const updateNotificationSettings = async (settings)=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/user/notification-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(settings)\n            });\n            if (response.ok) {\n                setNotifications(settings);\n            }\n        } catch (error) {\n            console.error('Failed to update notification settings:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const updateProfile = async ()=>{\n        try {\n            setSaving(true);\n            // Profile update API call would go here\n            console.log('Profile update:', profileData);\n        } catch (error) {\n            console.error('Failed to update profile:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const updatePassword = async ()=>{\n        if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n            showMessage({\n                title: 'Password Mismatch',\n                message: 'New passwords do not match',\n                variant: 'error'\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            // Password update API call would go here\n            console.log('Password update');\n            setPasswordForm({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Failed to update password:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleProfilePictureUpload = async (file)=>{\n        try {\n            var _user_refreshUser;\n            setUploadingPicture(true);\n            const formData = new FormData();\n            formData.append('profilePicture', file);\n            const response = await fetch('/api/user/profile-picture', {\n                method: 'POST',\n                credentials: 'include',\n                body: formData\n            });\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to upload profile picture');\n            }\n            // Refresh user data to get updated profile picture\n            await (user === null || user === void 0 ? void 0 : (_user_refreshUser = user.refreshUser) === null || _user_refreshUser === void 0 ? void 0 : _user_refreshUser.call(user));\n            showMessage({\n                title: 'Success',\n                message: 'Profile picture updated successfully',\n                variant: 'success'\n            });\n        } catch (error) {\n            console.error('Profile picture upload error:', error);\n            showMessage({\n                title: 'Upload Failed',\n                message: error instanceof Error ? error.message : 'Failed to upload profile picture',\n                variant: 'error'\n            });\n        } finally{\n            setUploadingPicture(false);\n        }\n    };\n    const handleProfilePictureRemove = async ()=>{\n        try {\n            var _user_refreshUser;\n            setUploadingPicture(true);\n            const response = await fetch('/api/user/profile-picture', {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to remove profile picture');\n            }\n            // Refresh user data to get updated profile picture\n            await (user === null || user === void 0 ? void 0 : (_user_refreshUser = user.refreshUser) === null || _user_refreshUser === void 0 ? void 0 : _user_refreshUser.call(user));\n            showMessage({\n                title: 'Success',\n                message: 'Profile picture removed successfully',\n                variant: 'success'\n            });\n        } catch (error) {\n            console.error('Profile picture removal error:', error);\n            showMessage({\n                title: 'Removal Failed',\n                message: error instanceof Error ? error.message : 'Failed to remove profile picture',\n                variant: 'error'\n            });\n        } finally{\n            setUploadingPicture(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, undefined);\n    }\n    const tabs = [\n        {\n            id: 'profile',\n            label: 'Profile Information',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security Settings',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notification Settings',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'billing',\n            label: 'Billing & Payments',\n            icon: _barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ];\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'profile':\n                return renderProfileContent();\n            case 'security':\n                return renderSecurityContent();\n            case 'notifications':\n                return renderNotificationContent();\n            case 'billing':\n                return renderBillingContent();\n            default:\n                return renderProfileContent();\n        }\n    };\n    const renderProfileContent = ()=>{\n        const isKycSubmittedOrApproved = (user === null || user === void 0 ? void 0 : user.kycStatus) === 'PENDING' || (user === null || user === void 0 ? void 0 : user.kycStatus) === 'APPROVED';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Profile Information\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 font-medium\",\n                                            children: \"Profile names cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-1\",\n                                    children: \"Your identity has been verified and profile information is now locked for security.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"First Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"text\",\n                                            value: profileData.firstName,\n                                            onChange: (e)=>setProfileData((prev)=>({\n                                                        ...prev,\n                                                        firstName: e.target.value\n                                                    })),\n                                            disabled: isKycSubmittedOrApproved,\n                                            className: isKycSubmittedOrApproved ? \"bg-gray-100 border-gray-300 text-gray-500\" : \"bg-white border-gray-300 text-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Last Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: \"text\",\n                                            value: profileData.lastName,\n                                            onChange: (e)=>setProfileData((prev)=>({\n                                                        ...prev,\n                                                        lastName: e.target.value\n                                                    })),\n                                            disabled: isKycSubmittedOrApproved,\n                                            className: isKycSubmittedOrApproved ? \"bg-gray-100 border-gray-300 text-gray-500\" : \"bg-white border-gray-300 text-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isKycSubmittedOrApproved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Cannot be changed after KYC submission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"email\",\n                                    value: profileData.email,\n                                    disabled: true,\n                                    className: \"bg-gray-100 border-gray-300 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Email cannot be changed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Referral ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"text\",\n                                    value: profileData.referralId,\n                                    disabled: true,\n                                    className: \"bg-gray-100 border-gray-300 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Your unique referral identifier\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updateProfile,\n                            disabled: saving || isKycSubmittedOrApproved,\n                            className: \"w-full bg-yellow-500 hover:bg-yellow-600 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, undefined),\n                                saving ? 'Saving...' : isKycSubmittedOrApproved ? 'Profile Locked' : 'Save Profile'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 281,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderSecurityContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Security Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Current Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            value: passwordForm.currentPassword,\n                                            onChange: (e)=>setPasswordForm((prev)=>({\n                                                        ...prev,\n                                                        currentPassword: e.target.value\n                                                    })),\n                                            className: \"bg-white border-gray-300 text-gray-900 pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: passwordForm.newPassword,\n                                    onChange: (e)=>setPasswordForm((prev)=>({\n                                                ...prev,\n                                                newPassword: e.target.value\n                                            })),\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: passwordForm.confirmPassword,\n                                    onChange: (e)=>setPasswordForm((prev)=>({\n                                                ...prev,\n                                                confirmPassword: e.target.value\n                                            })),\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updatePassword,\n                            disabled: saving || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword,\n                            className: \"w-full bg-red-500 hover:bg-red-600 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 11\n                                }, undefined),\n                                saving ? 'Updating...' : 'Update Password'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 379,\n            columnNumber: 5\n        }, undefined);\n    const renderNotificationContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Notification Settings\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Email Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Receive updates via email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.emailNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    emailNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Push Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Browser notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.pushNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    pushNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"SMS Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Text message alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: notifications.smsNotifications,\n                                            onChange: (e)=>updateNotificationSettings({\n                                                    ...notifications,\n                                                    smsNotifications: e.target.checked\n                                                }),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 446,\n            columnNumber: 5\n        }, undefined);\n    const updateWithdrawalAddress = async ()=>{\n        try {\n            setSaving(true);\n            // Validate USDT TRC20 address format\n            if (withdrawalAddress && !withdrawalAddress.match(/^T[A-Za-z1-9]{33}$/)) {\n                showMessage({\n                    title: 'Invalid Address',\n                    message: 'Invalid USDT TRC20 address format. Address must start with \"T\" and be 34 characters long.',\n                    variant: 'error'\n                });\n                return;\n            }\n            const response = await fetch('/api/user/withdrawal-address', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    withdrawalAddress: withdrawalAddress.trim()\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Response not OK:', response.status, errorText);\n                throw new Error(\"Server error: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update withdrawal address');\n            }\n            // Show success message\n            showMessage({\n                title: 'Success',\n                message: 'Withdrawal address updated successfully!',\n                variant: 'success'\n            });\n        } catch (error) {\n            console.error('Failed to update withdrawal address:', error);\n            showMessage({\n                title: 'Error',\n                message: \"Error: \".concat(error.message),\n                variant: 'error'\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const renderBillingContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Billing & Payments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Default Withdrawal Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"text\",\n                                    value: withdrawalAddress,\n                                    onChange: (e)=>setWithdrawalAddress(e.target.value),\n                                    placeholder: \"Enter your USDT TRC20 address\",\n                                    className: \"bg-white border-gray-300 text-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"This address will be automatically filled in withdrawal forms. Only USDT TRC20 addresses are supported.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-800 mb-1\",\n                                                children: \"Security Notice\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: \"Your withdrawal address is encrypted and stored securely. You can update it anytime, but make sure to double-check the address as transactions cannot be reversed.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: updateWithdrawalAddress,\n                            disabled: saving,\n                            className: \"w-full bg-yellow-500 hover:bg-yellow-600 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Eye_EyeOff_Key_Mail_Save_Settings_Shield_Smartphone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 11\n                                }, undefined),\n                                saving ? 'Saving...' : 'Save Withdrawal Address'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 617,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n            lineNumber: 580,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Profile & Settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Manage your account information and preferences\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 632,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: tabs.map((tab)=>{\n                        const Icon = tab.icon;\n                        const isActive = activeTab === tab.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"\\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\\n                  \".concat(isActive ? 'border-yellow-500 text-yellow-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \"\\n                \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                    lineNumber: 639,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: renderTabContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 665,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBoxComponent, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n                lineNumber: 670,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\UserProfileSettings.tsx\",\n        lineNumber: 630,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UserProfileSettings, \"DGT74mfTk7N0X7+bUy0j1TqKodI=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox\n    ];\n});\n_c = UserProfileSettings;\nvar _c;\n$RefreshReg$(_c, \"UserProfileSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/UserProfileSettings.tsx\n"));

/***/ })

});