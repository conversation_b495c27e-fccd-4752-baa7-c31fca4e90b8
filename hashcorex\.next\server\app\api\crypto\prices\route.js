/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/crypto/prices/route";
exports.ids = ["app/api/crypto/prices/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcrypto%2Fprices%2Froute&page=%2Fapi%2Fcrypto%2Fprices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcrypto%2Fprices%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcrypto%2Fprices%2Froute&page=%2Fapi%2Fcrypto%2Fprices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcrypto%2Fprices%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_crypto_prices_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/crypto/prices/route.ts */ \"(rsc)/./src/app/api/crypto/prices/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/crypto/prices/route\",\n        pathname: \"/api/crypto/prices\",\n        filename: \"route\",\n        bundlePath: \"app/api/crypto/prices/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\crypto\\\\prices\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_crypto_prices_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjcnlwdG8lMkZwcmljZXMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmNyeXB0byUyRnByaWNlcyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmNyeXB0byUyRnByaWNlcyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNkcmVhbSU1Q0Rlc2t0b3AlNUNIYXNoX01pbmluZ3MlNUNoYXNoY29yZXglNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2RyZWFtJTVDRGVza3RvcCU1Q0hhc2hfTWluaW5ncyU1Q2hhc2hjb3JleCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDMkM7QUFDeEg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxjcnlwdG9cXFxccHJpY2VzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jcnlwdG8vcHJpY2VzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvY3J5cHRvL3ByaWNlc1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvY3J5cHRvL3ByaWNlcy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxjcnlwdG9cXFxccHJpY2VzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcrypto%2Fprices%2Froute&page=%2Fapi%2Fcrypto%2Fprices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcrypto%2Fprices%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/crypto/prices/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/crypto/prices/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Cache for crypto prices (in-memory cache for simplicity)\nlet priceCache = null;\nconst CACHE_DURATION = 60000; // 1 minute in milliseconds\n// Top cryptocurrencies to fetch\nconst CRYPTO_IDS = [\n    'bitcoin',\n    'ethereum',\n    'tether',\n    'binancecoin',\n    'solana',\n    'usd-coin',\n    'xrp',\n    'staked-ether',\n    'dogecoin',\n    'cardano',\n    'tron',\n    'avalanche-2',\n    'chainlink',\n    'polygon',\n    'wrapped-bitcoin',\n    'internet-computer',\n    'near',\n    'uniswap',\n    'litecoin',\n    'dai',\n    'ethereum-classic',\n    'stellar',\n    'monero',\n    'bitcoin-cash',\n    'cosmos'\n];\n// GET - Fetch crypto prices\nasync function GET(request) {\n    try {\n        // Check if we have cached data that's still fresh\n        if (priceCache && Date.now() - priceCache.timestamp < CACHE_DURATION) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: priceCache.data,\n                cached: true,\n                timestamp: priceCache.timestamp\n            });\n        }\n        // Fetch fresh data from CoinGecko API\n        const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${CRYPTO_IDS.join(',')}&vs_currencies=usd&include_24hr_change=true`, {\n            headers: {\n                'Accept': 'application/json',\n                'User-Agent': 'HashCoreX/1.0'\n            },\n            // Add timeout\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            throw new Error(`CoinGecko API error: ${response.status}`);\n        }\n        const data = await response.json();\n        // Transform the data to a more usable format\n        const transformedData = CRYPTO_IDS.map((id)=>{\n            const priceData = data[id];\n            if (!priceData) return null;\n            return {\n                id,\n                symbol: getCryptoSymbol(id),\n                name: getCryptoName(id),\n                current_price: priceData.usd,\n                price_change_percentage_24h: priceData.usd_24h_change || 0,\n                image: getCryptoImage(id)\n            };\n        }).filter(Boolean);\n        // Update cache\n        priceCache = {\n            data: transformedData,\n            timestamp: Date.now()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: transformedData,\n            cached: false,\n            timestamp: Date.now()\n        });\n    } catch (error) {\n        console.error('Error fetching crypto prices:', error);\n        // Return cached data if available, even if stale\n        if (priceCache) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: priceCache.data,\n                cached: true,\n                stale: true,\n                timestamp: priceCache.timestamp,\n                error: 'Using cached data due to API error'\n            });\n        }\n        // Return fallback static data if no cache available\n        const fallbackData = getFallbackCryptoData();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: fallbackData,\n            fallback: true,\n            error: error instanceof Error ? error.message : 'Unknown error',\n            timestamp: Date.now()\n        });\n    }\n}\n// Helper functions\nfunction getCryptoSymbol(id) {\n    const symbolMap = {\n        'bitcoin': 'BTC',\n        'ethereum': 'ETH',\n        'tether': 'USDT',\n        'binancecoin': 'BNB',\n        'solana': 'SOL',\n        'usd-coin': 'USDC',\n        'xrp': 'XRP',\n        'staked-ether': 'stETH',\n        'dogecoin': 'DOGE',\n        'cardano': 'ADA',\n        'tron': 'TRX',\n        'avalanche-2': 'AVAX',\n        'chainlink': 'LINK',\n        'polygon': 'MATIC',\n        'wrapped-bitcoin': 'WBTC',\n        'internet-computer': 'ICP',\n        'near': 'NEAR',\n        'uniswap': 'UNI',\n        'litecoin': 'LTC',\n        'dai': 'DAI',\n        'ethereum-classic': 'ETC',\n        'stellar': 'XLM',\n        'monero': 'XMR',\n        'bitcoin-cash': 'BCH',\n        'cosmos': 'ATOM'\n    };\n    return symbolMap[id] || id.toUpperCase();\n}\nfunction getCryptoName(id) {\n    const nameMap = {\n        'bitcoin': 'Bitcoin',\n        'ethereum': 'Ethereum',\n        'tether': 'Tether',\n        'binancecoin': 'BNB',\n        'solana': 'Solana',\n        'usd-coin': 'USD Coin',\n        'xrp': 'XRP',\n        'staked-ether': 'Lido Staked Ether',\n        'dogecoin': 'Dogecoin',\n        'cardano': 'Cardano',\n        'tron': 'TRON',\n        'avalanche-2': 'Avalanche',\n        'chainlink': 'Chainlink',\n        'polygon': 'Polygon',\n        'wrapped-bitcoin': 'Wrapped Bitcoin',\n        'internet-computer': 'Internet Computer',\n        'near': 'NEAR Protocol',\n        'uniswap': 'Uniswap',\n        'litecoin': 'Litecoin',\n        'dai': 'Dai',\n        'ethereum-classic': 'Ethereum Classic',\n        'stellar': 'Stellar',\n        'monero': 'Monero',\n        'bitcoin-cash': 'Bitcoin Cash',\n        'cosmos': 'Cosmos'\n    };\n    return nameMap[id] || id;\n}\nfunction getCryptoImage(id) {\n    return `/crypto-icons/${id}.png`;\n}\n// Fallback data in case all APIs fail\nfunction getFallbackCryptoData() {\n    return [\n        {\n            id: 'bitcoin',\n            symbol: 'BTC',\n            name: 'Bitcoin',\n            current_price: 45000,\n            price_change_percentage_24h: 2.5,\n            image: '/crypto-icons/bitcoin.png'\n        },\n        {\n            id: 'ethereum',\n            symbol: 'ETH',\n            name: 'Ethereum',\n            current_price: 2800,\n            price_change_percentage_24h: 1.8,\n            image: '/crypto-icons/ethereum.png'\n        },\n        {\n            id: 'tether',\n            symbol: 'USDT',\n            name: 'Tether',\n            current_price: 1.00,\n            price_change_percentage_24h: 0.1,\n            image: '/crypto-icons/tether.png'\n        },\n        {\n            id: 'binancecoin',\n            symbol: 'BNB',\n            name: 'BNB',\n            current_price: 320,\n            price_change_percentage_24h: -0.5,\n            image: '/crypto-icons/binancecoin.png'\n        },\n        {\n            id: 'solana',\n            symbol: 'SOL',\n            name: 'Solana',\n            current_price: 95,\n            price_change_percentage_24h: 3.2,\n            image: '/crypto-icons/solana.png'\n        },\n        {\n            id: 'xrp',\n            symbol: 'XRP',\n            name: 'XRP',\n            current_price: 0.52,\n            price_change_percentage_24h: -1.2,\n            image: '/crypto-icons/xrp.png'\n        },\n        {\n            id: 'dogecoin',\n            symbol: 'DOGE',\n            name: 'Dogecoin',\n            current_price: 0.08,\n            price_change_percentage_24h: 4.1,\n            image: '/crypto-icons/dogecoin.png'\n        },\n        {\n            id: 'cardano',\n            symbol: 'ADA',\n            name: 'Cardano',\n            current_price: 0.45,\n            price_change_percentage_24h: 1.5,\n            image: '/crypto-icons/cardano.png'\n        },\n        {\n            id: 'tron',\n            symbol: 'TRX',\n            name: 'TRON',\n            current_price: 0.11,\n            price_change_percentage_24h: 2.8,\n            image: '/crypto-icons/tron.png'\n        },\n        {\n            id: 'avalanche-2',\n            symbol: 'AVAX',\n            name: 'Avalanche',\n            current_price: 28,\n            price_change_percentage_24h: -0.8,\n            image: '/crypto-icons/avalanche-2.png'\n        }\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/crypto/prices/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcrypto%2Fprices%2Froute&page=%2Fapi%2Fcrypto%2Fprices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcrypto%2Fprices%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();