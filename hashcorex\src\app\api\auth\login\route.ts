import { NextRequest, NextResponse } from 'next/server';
import { loginUser, validateEmail } from '@/lib/auth';
import { systemLogDb } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validation
    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Login user
    const result = await loginUser({ email, password });

    // Log successful login
    await systemLogDb.create({
      action: 'USER_LOGIN',
      userId: result.user.id,
      details: {
        email: result.user.email,
        loginTime: new Date().toISOString(),
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    // Set HTTP-only cookie
    const response = NextResponse.json({
      success: true,
      message: 'Login successful',
      data: {
        user: result.user,
        token: result.token,
      },
    });

    response.cookies.set('auth-token', result.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60, // 30 days
    });

    return response;

  } catch (error: any) {
    console.error('Login error:', error);

    // Log failed login attempt
    try {
      await systemLogDb.create({
        action: 'LOGIN_FAILED',
        details: {
          email: request.body?.email || 'unknown',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      });
    } catch (logError) {
      console.error('Failed to log login attempt:', logError);
    }
    
    return NextResponse.json(
      { success: false, error: error.message || 'Login failed' },
      { status: 401 }
    );
  }
}
