/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mining-units/route";
exports.ids = ["app/api/mining-units/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmining-units%2Froute&page=%2Fapi%2Fmining-units%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmining-units%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmining-units%2Froute&page=%2Fapi%2Fmining-units%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmining-units%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_mining_units_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/mining-units/route.ts */ \"(rsc)/./src/app/api/mining-units/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mining-units/route\",\n        pathname: \"/api/mining-units\",\n        filename: \"route\",\n        bundlePath: \"app/api/mining-units/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\mining-units\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_mining_units_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmining-units%2Froute&page=%2Fapi%2Fmining-units%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmining-units%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/mining-units/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/mining-units/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n/* harmony import */ var _lib_referral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/referral */ \"(rsc)/./src/lib/referral.ts\");\n\n\n\n\n\n// GET - Fetch user's mining units with detailed earnings breakdown\nasync function GET(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        // Get all mining units with detailed earnings breakdown\n        const miningUnits = await (0,_lib_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_3__.getUserMiningUnitsWithEarnings)(user.id);\n        // Calculate additional information for each unit\n        const enrichedMiningUnits = miningUnits.map((unit)=>{\n            const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n            const maxEarnings = unit.investmentAmount * 5;\n            const progress = totalEarnings / maxEarnings * 100;\n            const remainingCapacity = Math.max(0, maxEarnings - totalEarnings);\n            return {\n                ...unit,\n                totalEarningsCalculated: totalEarnings,\n                progressPercentage: Math.min(progress, 100),\n                remainingCapacity,\n                maxEarnings,\n                willExpireSoon: progress >= 95\n            };\n        });\n        // Sort by creation date for FIFO order\n        const sortedUnits = enrichedMiningUnits.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: sortedUnits,\n            summary: {\n                totalUnits: sortedUnits.length,\n                activeUnits: sortedUnits.filter((unit)=>unit.status === 'ACTIVE').length,\n                expiredUnits: sortedUnits.filter((unit)=>unit.status === 'EXPIRED').length,\n                totalMiningEarnings: sortedUnits.reduce((sum, unit)=>sum + unit.miningEarnings, 0),\n                totalReferralEarnings: sortedUnits.reduce((sum, unit)=>sum + unit.referralEarnings, 0),\n                totalBinaryEarnings: sortedUnits.reduce((sum, unit)=>sum + unit.binaryEarnings, 0),\n                totalEarnings: sortedUnits.reduce((sum, unit)=>sum + (unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings), 0),\n                totalInvestment: sortedUnits.reduce((sum, unit)=>sum + unit.investmentAmount, 0),\n                totalMiningPower: sortedUnits.reduce((sum, unit)=>sum + unit.thsAmount, 0)\n            }\n        });\n    } catch (error) {\n        console.error('Mining units fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch mining units'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Purchase new mining unit\nasync function POST(request) {\n    try {\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { thsAmount, investmentAmount } = body;\n        // Validation\n        if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid TH/s amount or investment amount'\n            }, {\n                status: 400\n            });\n        }\n        // Get minimum purchase amount from admin settings\n        const minPurchase = parseFloat(await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('MINIMUM_PURCHASE') || '50');\n        if (investmentAmount < minPurchase) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Minimum purchase amount is $${minPurchase}`\n            }, {\n                status: 400\n            });\n        }\n        // Get TH/s price from admin settings\n        const thsPrice = parseFloat(await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('THS_PRICE') || '50');\n        const expectedAmount = thsAmount * thsPrice;\n        // Allow small rounding differences (within 1%)\n        if (Math.abs(investmentAmount - expectedAmount) > expectedAmount * 0.01) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Investment amount does not match TH/s price'\n            }, {\n                status: 400\n            });\n        }\n        // Calculate dynamic ROI based on unit size\n        const { calculateDynamicROI } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_mining_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/mining */ \"(rsc)/./src/lib/mining.ts\"));\n        const dynamicROI = await calculateDynamicROI(thsAmount);\n        // Check wallet balance before purchase\n        const walletBalance = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.walletBalanceDb.getOrCreate(user.id);\n        if (walletBalance.availableBalance < investmentAmount) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Insufficient balance. Available: $${walletBalance.availableBalance.toFixed(2)}, Required: $${investmentAmount.toFixed(2)}`\n            }, {\n                status: 400\n            });\n        }\n        // Use the calculated dynamic ROI\n        const dailyROI = dynamicROI;\n        // Create mining unit\n        const miningUnit = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.miningUnitDb.create({\n            userId: user.id,\n            thsAmount,\n            investmentAmount,\n            dailyROI\n        });\n        // Deduct amount from wallet balance\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.walletBalanceDb.updateBalance(user.id, {\n            availableBalance: walletBalance.availableBalance - investmentAmount\n        });\n        // Create purchase transaction\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.transactionDb.create({\n            userId: user.id,\n            type: 'PURCHASE',\n            amount: investmentAmount,\n            description: `Mining unit purchase - ${thsAmount} TH/s`,\n            status: 'COMPLETED'\n        });\n        // Note: User active status is now computed dynamically for binary tree display\n        // Process referral commissions and binary points\n        const sponsorInfo = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_4__.getSponsorInfo)(user.id);\n        if (sponsorInfo) {\n            try {\n                // Process direct referral bonus (10% to sponsor's wallet)\n                const bonusAmount = await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_4__.processDirectReferralBonus)(sponsorInfo.id, investmentAmount, user.id);\n                console.log(`Direct referral bonus of $${bonusAmount} added to sponsor ${sponsorInfo.id}`);\n                // Add binary points to active upliners ($100 = 1 point)\n                await (0,_lib_referral__WEBPACK_IMPORTED_MODULE_4__.addBinaryPoints)(user.id, investmentAmount);\n                console.log(`Binary points added for investment of $${investmentAmount}`);\n            } catch (commissionError) {\n                console.error('Error processing commissions:', commissionError);\n            // Don't fail the purchase if commission processing fails\n            }\n        }\n        // Log the purchase\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.systemLogDb.create({\n            action: 'MINING_UNIT_PURCHASED',\n            userId: user.id,\n            details: {\n                miningUnitId: miningUnit.id,\n                thsAmount,\n                investmentAmount,\n                dailyROI,\n                sponsorId: sponsorInfo?.id\n            },\n            ipAddress: request.headers.get('x-forwarded-for') || 'unknown',\n            userAgent: request.headers.get('user-agent') || 'unknown'\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Mining unit purchased successfully',\n            data: miningUnit\n        });\n    } catch (error) {\n        console.error('Mining unit purchase error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to purchase mining unit'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/mining-units/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30d';\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, 12);\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/miningUnitEarnings.ts":
/*!***************************************!*\
  !*** ./src/lib/miningUnitEarnings.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allocateEarningsToUnits: () => (/* binding */ allocateEarningsToUnits),\n/* harmony export */   calculateRemainingCapacity: () => (/* binding */ calculateRemainingCapacity),\n/* harmony export */   expireMiningUnit: () => (/* binding */ expireMiningUnit),\n/* harmony export */   getActiveMiningUnitsFIFO: () => (/* binding */ getActiveMiningUnitsFIFO),\n/* harmony export */   getMiningUnitEarningsHistory: () => (/* binding */ getMiningUnitEarningsHistory),\n/* harmony export */   getUserMiningUnitsWithEarnings: () => (/* binding */ getUserMiningUnitsWithEarnings),\n/* harmony export */   shouldExpireUnit: () => (/* binding */ shouldExpireUnit)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n/**\n * Get active mining units for a user ordered by creation date (FIFO)\n */ async function getActiveMiningUnitsFIFO(userId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n        where: {\n            userId,\n            status: 'ACTIVE',\n            expiryDate: {\n                gt: new Date()\n            }\n        },\n        orderBy: {\n            createdAt: 'asc'\n        }\n    });\n}\n/**\n * Calculate remaining earning capacity for a mining unit (5x - current earnings)\n */ function calculateRemainingCapacity(unit) {\n    const maxEarnings = unit.investmentAmount * 5;\n    const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    return Math.max(0, maxEarnings - currentTotalEarnings);\n}\n/**\n * Check if a mining unit should expire based on 5x earnings\n */ function shouldExpireUnit(unit) {\n    const maxEarnings = unit.investmentAmount * 5;\n    const currentTotalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    return currentTotalEarnings >= maxEarnings;\n}\n/**\n * Allocate earnings to mining units using FIFO logic\n * Returns array of allocations showing how much was allocated to each unit\n */ async function allocateEarningsToUnits(userId, totalAmount, earningType, transactionId, description) {\n    const activeMiningUnits = await getActiveMiningUnitsFIFO(userId);\n    if (activeMiningUnits.length === 0) {\n        throw new Error('No active mining units found for earnings allocation');\n    }\n    const allocations = [];\n    let remainingAmount = totalAmount;\n    for (const unit of activeMiningUnits){\n        if (remainingAmount <= 0) break;\n        const remainingCapacity = calculateRemainingCapacity(unit);\n        if (remainingCapacity <= 0) {\n            continue;\n        }\n        // Allocate the minimum of remaining amount or remaining capacity\n        const allocationAmount = Math.min(remainingAmount, remainingCapacity);\n        if (allocationAmount > 0) {\n            // Update the mining unit earnings based on type\n            const updateData = {};\n            switch(earningType){\n                case 'MINING_EARNINGS':\n                    updateData.miningEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n                case 'DIRECT_REFERRAL':\n                    updateData.referralEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n                case 'BINARY_BONUS':\n                    updateData.binaryEarnings = {\n                        increment: allocationAmount\n                    };\n                    break;\n            }\n            // Update total earned for legacy compatibility\n            updateData.totalEarned = {\n                increment: allocationAmount\n            };\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n                where: {\n                    id: unit.id\n                },\n                data: updateData\n            });\n            // Create earnings allocation record\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnitEarningsAllocation.create({\n                data: {\n                    miningUnitId: unit.id,\n                    transactionId,\n                    earningType,\n                    amount: allocationAmount,\n                    description\n                }\n            });\n            allocations.push({\n                miningUnitId: unit.id,\n                amount: allocationAmount,\n                remainingCapacity: remainingCapacity - allocationAmount\n            });\n            remainingAmount -= allocationAmount;\n            // Check if unit should expire after this allocation\n            const updatedUnit = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findUnique({\n                where: {\n                    id: unit.id\n                }\n            });\n            if (updatedUnit && shouldExpireUnit(updatedUnit)) {\n                await expireMiningUnit(unit.id, '5x_investment_reached');\n            }\n        }\n    }\n    // If there's still remaining amount, it means all units are at capacity\n    if (remainingAmount > 0) {\n        console.warn(`Unable to allocate ${remainingAmount} to mining units - all units at capacity`);\n        // Log this situation\n        await _lib_database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'EARNINGS_ALLOCATION_OVERFLOW',\n            userId,\n            details: {\n                totalAmount,\n                allocatedAmount: totalAmount - remainingAmount,\n                overflowAmount: remainingAmount,\n                earningType,\n                reason: 'all_units_at_capacity'\n            }\n        });\n    }\n    return allocations;\n}\n/**\n * Expire a mining unit and log the action\n */ async function expireMiningUnit(unitId, reason) {\n    const unit = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findUnique({\n        where: {\n            id: unitId\n        }\n    });\n    if (!unit) {\n        throw new Error(`Mining unit ${unitId} not found`);\n    }\n    await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n        where: {\n            id: unitId\n        },\n        data: {\n            status: 'EXPIRED'\n        }\n    });\n    const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    await _lib_database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n        action: 'MINING_UNIT_EXPIRED',\n        userId: unit.userId,\n        details: {\n            miningUnitId: unitId,\n            reason,\n            totalEarned: totalEarnings,\n            miningEarnings: unit.miningEarnings,\n            referralEarnings: unit.referralEarnings,\n            binaryEarnings: unit.binaryEarnings,\n            investmentAmount: unit.investmentAmount,\n            multiplier: totalEarnings / unit.investmentAmount\n        }\n    });\n    console.log(`Mining unit ${unitId} expired due to ${reason}. Total earnings: ${totalEarnings}`);\n}\n/**\n * Get detailed earnings breakdown for a user's mining units\n */ async function getUserMiningUnitsWithEarnings(userId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n        where: {\n            userId\n        },\n        orderBy: {\n            createdAt: 'asc'\n        }\n    });\n}\n/**\n * Get earnings allocation history for a mining unit\n */ async function getMiningUnitEarningsHistory(miningUnitId) {\n    return await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnitEarningsAllocation.findMany({\n        where: {\n            miningUnitId\n        },\n        include: {\n            transaction: true\n        },\n        orderBy: {\n            allocatedAt: 'desc'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/miningUnitEarnings.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/referral.ts":
/*!*****************************!*\
  !*** ./src/lib/referral.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addBinaryPoints: () => (/* binding */ addBinaryPoints),\n/* harmony export */   bulkUpdateTreeCounts: () => (/* binding */ bulkUpdateTreeCounts),\n/* harmony export */   calculateDownlineCount: () => (/* binding */ calculateDownlineCount),\n/* harmony export */   getBinaryTreeStructure: () => (/* binding */ getBinaryTreeStructure),\n/* harmony export */   getCachedDownlineCounts: () => (/* binding */ getCachedDownlineCounts),\n/* harmony export */   getDetailedTeamStats: () => (/* binding */ getDetailedTeamStats),\n/* harmony export */   getDirectReferralCount: () => (/* binding */ getDirectReferralCount),\n/* harmony export */   getSponsorInfo: () => (/* binding */ getSponsorInfo),\n/* harmony export */   getTotalTeamCount: () => (/* binding */ getTotalTeamCount),\n/* harmony export */   getTreeHealthStats: () => (/* binding */ getTreeHealthStats),\n/* harmony export */   getUsersByGeneration: () => (/* binding */ getUsersByGeneration),\n/* harmony export */   hasActiveMiningUnits: () => (/* binding */ hasActiveMiningUnits),\n/* harmony export */   loadNodeChildren: () => (/* binding */ loadNodeChildren),\n/* harmony export */   placeUserByReferralType: () => (/* binding */ placeUserByReferralType),\n/* harmony export */   placeUserInBinaryTree: () => (/* binding */ placeUserInBinaryTree),\n/* harmony export */   placeUserInLeftSideOnly: () => (/* binding */ placeUserInLeftSideOnly),\n/* harmony export */   placeUserInRightSideOnly: () => (/* binding */ placeUserInRightSideOnly),\n/* harmony export */   placeUserInSpecificSide: () => (/* binding */ placeUserInSpecificSide),\n/* harmony export */   processBinaryMatching: () => (/* binding */ processBinaryMatching),\n/* harmony export */   processDirectReferralBonus: () => (/* binding */ processDirectReferralBonus),\n/* harmony export */   searchUsersInTree: () => (/* binding */ searchUsersInTree),\n/* harmony export */   updateCachedDownlineCounts: () => (/* binding */ updateCachedDownlineCounts)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./miningUnitEarnings */ \"(rsc)/./src/lib/miningUnitEarnings.ts\");\n\n\n\n// Check if user has active mining units (for binary tree display)\nasync function hasActiveMiningUnits(userId) {\n    try {\n        const activeMiningUnits = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.count({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n        return activeMiningUnits > 0;\n    } catch (error) {\n        console.error('Error checking active mining units:', error);\n        return false;\n    }\n}\n// Calculate total downline count for a specific side\nasync function calculateDownlineCount(userId, side) {\n    try {\n        const downlineUsers = await getDownlineUsers(userId, side);\n        return downlineUsers.length;\n    } catch (error) {\n        console.error('Downline count calculation error:', error);\n        return 0;\n    }\n}\n// Find the optimal placement position in the weaker leg\nasync function findOptimalPlacementPosition(referrerId) {\n    try {\n        // Calculate total downline counts for both sides\n        const leftDownlineCount = await calculateDownlineCount(referrerId, 'LEFT');\n        const rightDownlineCount = await calculateDownlineCount(referrerId, 'RIGHT');\n        // Determine weaker leg based on total downline count\n        const weakerSide = leftDownlineCount <= rightDownlineCount ? 'LEFT' : 'RIGHT';\n        // Find the next available spot in the weaker leg\n        const availableSpot = await findNextAvailableSpotInLeg(referrerId, weakerSide);\n        if (availableSpot) {\n            return availableSpot;\n        }\n        // Fallback: if no spot found in weaker leg, try the other side\n        const strongerSide = weakerSide === 'LEFT' ? 'RIGHT' : 'LEFT';\n        const fallbackSpot = await findNextAvailableSpotInLeg(referrerId, strongerSide);\n        if (fallbackSpot) {\n            return fallbackSpot;\n        }\n        // Final fallback: place directly under referrer\n        const existingReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(referrerId);\n        const hasLeft = existingReferrals.some((r)=>r.placementSide === 'LEFT');\n        const hasRight = existingReferrals.some((r)=>r.placementSide === 'RIGHT');\n        if (!hasLeft) {\n            return {\n                userId: referrerId,\n                side: 'LEFT'\n            };\n        } else if (!hasRight) {\n            return {\n                userId: referrerId,\n                side: 'RIGHT'\n            };\n        }\n        // If both sides are occupied, place in the weaker side\n        return {\n            userId: referrerId,\n            side: weakerSide\n        };\n    } catch (error) {\n        console.error('Optimal placement position error:', error);\n        // Fallback to left side\n        return {\n            userId: referrerId,\n            side: 'LEFT'\n        };\n    }\n}\n// Enhanced place new user in binary tree with weaker leg algorithm\nasync function placeUserInBinaryTree(referrerId, newUserId) {\n    try {\n        // Find optimal placement position using advanced weaker leg algorithm\n        const optimalPosition = await findOptimalPlacementPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's left/right referral IDs\n        const updateData = optimalPosition.side === 'LEFT' ? {\n            leftReferralId: newUserId\n        } : {\n            rightReferralId: newUserId\n        };\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: updateData\n        });\n        // Create sponsor relationship (separate from binary placement)\n        // The sponsor is always the original referrer, regardless of binary placement\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Binary tree placement error:', error);\n        throw error;\n    }\n}\n// Create sponsor relationship (separate from binary placement)\nasync function createSponsorRelationship(sponsorId, newUserId) {\n    try {\n        // Update the new user's referrerId field to track sponsor\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: newUserId\n            },\n            data: {\n                referrerId: sponsorId\n            }\n        });\n        // Update sponsor's direct referral count\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: sponsorId\n            },\n            data: {\n                directReferralCount: {\n                    increment: 1\n                },\n                updatedAt: new Date()\n            }\n        });\n        // Mark referral as direct sponsor if the binary placement parent is the same as sponsor\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.updateMany({\n            where: {\n                referrerId: sponsorId,\n                referredId: newUserId\n            },\n            data: {\n                isDirectSponsor: true\n            }\n        });\n    } catch (error) {\n        console.error('Sponsor relationship creation error:', error);\n    // Don't throw error as this is supplementary to binary placement\n    }\n}\n// Update cached downline counts for a user\nasync function updateCachedDownlineCounts(userId) {\n    try {\n        const leftCount = await calculateDownlineCount(userId, 'LEFT');\n        const rightCount = await calculateDownlineCount(userId, 'RIGHT');\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                totalLeftDownline: leftCount,\n                totalRightDownline: rightCount,\n                lastTreeUpdate: new Date()\n            }\n        });\n    } catch (error) {\n        console.error('Update cached downline counts error:', error);\n    }\n}\n// Get cached downline counts (with fallback to real-time calculation)\nasync function getCachedDownlineCounts(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                totalLeftDownline: true,\n                totalRightDownline: true,\n                lastTreeUpdate: true\n            }\n        });\n        if (!user) {\n            return {\n                left: 0,\n                right: 0,\n                total: 0\n            };\n        }\n        // Check if cache is recent (within last 30 minutes for more accurate counts)\n        const cacheAge = user.lastTreeUpdate ? Date.now() - user.lastTreeUpdate.getTime() : Infinity;\n        const cacheValidDuration = 30 * 60 * 1000; // 30 minutes\n        if (cacheAge < cacheValidDuration && user.totalLeftDownline !== null && user.totalRightDownline !== null) {\n            // Use cached values\n            return {\n                left: user.totalLeftDownline,\n                right: user.totalRightDownline,\n                total: user.totalLeftDownline + user.totalRightDownline\n            };\n        } else {\n            // Cache is stale or missing, recalculate and update\n            const leftCount = await calculateDownlineCount(userId, 'LEFT');\n            const rightCount = await calculateDownlineCount(userId, 'RIGHT');\n            // Update cache asynchronously\n            updateCachedDownlineCounts(userId).catch(console.error);\n            return {\n                left: leftCount,\n                right: rightCount,\n                total: leftCount + rightCount\n            };\n        }\n    } catch (error) {\n        console.error('Get cached downline counts error:', error);\n        return {\n            left: 0,\n            right: 0,\n            total: 0\n        };\n    }\n}\n// Find optimal placement in specific side with weaker leg logic (LEGACY - for backward compatibility)\nasync function findOptimalPlacementInSide(referrerId, targetSide) {\n    try {\n        // First, try to find the next available spot in the target side\n        const availableSpot = await findNextAvailableSpotInLeg(referrerId, targetSide);\n        if (availableSpot) {\n            return availableSpot;\n        }\n        // If no spot available, find the position with smallest downline in that side\n        const sideUsers = await getDownlineUsers(referrerId, targetSide);\n        // Find the user with the smallest total downline in the target side\n        let optimalUser = referrerId;\n        let minDownlineCount = Infinity;\n        for (const sideUser of sideUsers){\n            const leftCount = await calculateDownlineCount(sideUser.id, 'LEFT');\n            const rightCount = await calculateDownlineCount(sideUser.id, 'RIGHT');\n            const totalDownline = leftCount + rightCount;\n            if (totalDownline < minDownlineCount) {\n                // Check if this user has available spots\n                const userReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(sideUser.id);\n                const hasLeft = userReferrals.some((r)=>r.placementSide === 'LEFT');\n                const hasRight = userReferrals.some((r)=>r.placementSide === 'RIGHT');\n                if (!hasLeft || !hasRight) {\n                    minDownlineCount = totalDownline;\n                    optimalUser = sideUser.id;\n                }\n            }\n        }\n        // Determine which side to place in for the optimal user\n        const optimalUserReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(optimalUser);\n        const hasLeft = optimalUserReferrals.some((r)=>r.placementSide === 'LEFT');\n        const hasRight = optimalUserReferrals.some((r)=>r.placementSide === 'RIGHT');\n        if (!hasLeft) {\n            return {\n                userId: optimalUser,\n                side: 'LEFT'\n            };\n        } else if (!hasRight) {\n            return {\n                userId: optimalUser,\n                side: 'RIGHT'\n            };\n        }\n        // If both sides occupied, use weaker leg logic\n        const leftCount = await calculateDownlineCount(optimalUser, 'LEFT');\n        const rightCount = await calculateDownlineCount(optimalUser, 'RIGHT');\n        const weakerSide = leftCount <= rightCount ? 'LEFT' : 'RIGHT';\n        return {\n            userId: optimalUser,\n            side: weakerSide\n        };\n    } catch (error) {\n        console.error('Optimal placement in side error:', error);\n        return {\n            userId: referrerId,\n            side: targetSide\n        };\n    }\n}\n// NEW: Find deepest available position in LEFT side only (strict left-side placement)\nasync function findDeepestLeftPosition(referrerId) {\n    try {\n        // Start from the referrer and traverse down the LEFT side only\n        let currentUserId = referrerId;\n        let currentLevel = 0;\n        const maxDepth = 20; // Prevent infinite loops\n        while(currentLevel < maxDepth){\n            // Verify current user exists\n            const userExists = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true\n                }\n            });\n            if (!userExists) {\n                // User doesn't exist, fallback to referrer\n                return {\n                    userId: referrerId,\n                    side: 'LEFT'\n                };\n            }\n            // Check if current user has a LEFT spot available\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            const hasLeft = currentReferrals.some((r)=>r.placementSide === 'LEFT');\n            if (!hasLeft) {\n                // Found an available LEFT spot\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            // Move to the LEFT child and continue traversing\n            const leftChild = currentReferrals.find((r)=>r.placementSide === 'LEFT');\n            if (!leftChild) {\n                // This shouldn't happen if hasLeft is true, but safety check\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            currentUserId = leftChild.referredId;\n            currentLevel++;\n        }\n        // If we've reached max depth, place at the last position\n        return {\n            userId: currentUserId,\n            side: 'LEFT'\n        };\n    } catch (error) {\n        console.error('Find deepest left position error:', error);\n        return {\n            userId: referrerId,\n            side: 'LEFT'\n        };\n    }\n}\n// NEW: Find deepest available position in RIGHT side only (strict right-side placement)\nasync function findDeepestRightPosition(referrerId) {\n    try {\n        // Start from the referrer and traverse down the RIGHT side only\n        let currentUserId = referrerId;\n        let currentLevel = 0;\n        const maxDepth = 20; // Prevent infinite loops\n        while(currentLevel < maxDepth){\n            // Verify current user exists\n            const userExists = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true\n                }\n            });\n            if (!userExists) {\n                // User doesn't exist, fallback to referrer\n                return {\n                    userId: referrerId,\n                    side: 'RIGHT'\n                };\n            }\n            // Check if current user has a RIGHT spot available\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            const hasRight = currentReferrals.some((r)=>r.placementSide === 'RIGHT');\n            if (!hasRight) {\n                // Found an available RIGHT spot\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            // Move to the RIGHT child and continue traversing\n            const rightChild = currentReferrals.find((r)=>r.placementSide === 'RIGHT');\n            if (!rightChild) {\n                // This shouldn't happen if hasRight is true, but safety check\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            currentUserId = rightChild.referredId;\n            currentLevel++;\n        }\n        // If we've reached max depth, place at the last position\n        return {\n            userId: currentUserId,\n            side: 'RIGHT'\n        };\n    } catch (error) {\n        console.error('Find deepest right position error:', error);\n        return {\n            userId: referrerId,\n            side: 'RIGHT'\n        };\n    }\n}\n// Enhanced place user in specific side with weaker leg algorithm (LEGACY - for backward compatibility)\nasync function placeUserInSpecificSide(referrerId, newUserId, side) {\n    try {\n        // Find optimal placement position within the specified side\n        const optimalPosition = await findOptimalPlacementInSide(referrerId, side);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's referral ID\n        const updateData = optimalPosition.side === 'LEFT' ? {\n            leftReferralId: newUserId\n        } : {\n            rightReferralId: newUserId\n        };\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: updateData\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Specific side placement error:', error);\n        throw error;\n    }\n}\n// NEW: Place user strictly in LEFT side only (deepest available left position)\nasync function placeUserInLeftSideOnly(referrerId, newUserId) {\n    try {\n        // Find the deepest available position in the LEFT side\n        const optimalPosition = await findDeepestLeftPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's left referral ID\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: {\n                leftReferralId: newUserId\n            }\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Left side only placement error:', error);\n        throw error;\n    }\n}\n// NEW: Place user strictly in RIGHT side only (deepest available right position)\nasync function placeUserInRightSideOnly(referrerId, newUserId) {\n    try {\n        // Find the deepest available position in the RIGHT side\n        const optimalPosition = await findDeepestRightPosition(referrerId);\n        // Create referral relationship with the optimal parent\n        await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.create({\n            referrerId: optimalPosition.userId,\n            referredId: newUserId,\n            placementSide: optimalPosition.side\n        });\n        // Update the parent's right referral ID\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: optimalPosition.userId\n            },\n            data: {\n                rightReferralId: newUserId\n            }\n        });\n        // Create sponsor relationship (separate from binary placement)\n        await createSponsorRelationship(referrerId, newUserId);\n        // Update cached tree counts for affected users\n        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n        return optimalPosition.side;\n    } catch (error) {\n        console.error('Right side only placement error:', error);\n        throw error;\n    }\n}\n// NEW: Main placement function that routes to appropriate algorithm based on referral link type\nasync function placeUserByReferralType(referrerId, newUserId, referralType) {\n    try {\n        switch(referralType){\n            case 'left':\n                // Strict left-side placement: find deepest available left position\n                return await placeUserInLeftSideOnly(referrerId, newUserId);\n            case 'right':\n                // Strict right-side placement: find deepest available right position\n                return await placeUserInRightSideOnly(referrerId, newUserId);\n            case 'general':\n            default:\n                // Default weaker leg placement\n                return await placeUserInBinaryTree(referrerId, newUserId);\n        }\n    } catch (error) {\n        console.error('Placement by referral type error:', error);\n        throw error;\n    }\n}\n// Find next available spot in a specific leg\nasync function findNextAvailableSpotInLeg(rootUserId, targetSide) {\n    try {\n        // Get the first user in the target leg\n        const rootReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(rootUserId);\n        const firstInLeg = rootReferrals.find((r)=>r.placementSide === targetSide);\n        if (!firstInLeg) {\n            // The target side is completely empty\n            return {\n                userId: rootUserId,\n                side: targetSide\n            };\n        }\n        // Traverse down the leg to find the first available spot\n        const queue = [\n            firstInLeg.referredId\n        ];\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            const currentReferrals = await _database__WEBPACK_IMPORTED_MODULE_1__.referralDb.findByReferrerId(currentUserId);\n            // Check if this user has any empty spots\n            const hasLeft = currentReferrals.some((r)=>r.placementSide === 'LEFT');\n            const hasRight = currentReferrals.some((r)=>r.placementSide === 'RIGHT');\n            if (!hasLeft) {\n                return {\n                    userId: currentUserId,\n                    side: 'LEFT'\n                };\n            }\n            if (!hasRight) {\n                return {\n                    userId: currentUserId,\n                    side: 'RIGHT'\n                };\n            }\n            // Add children to queue for further traversal\n            currentReferrals.forEach((r)=>{\n                queue.push(r.referredId);\n            });\n        }\n        return null; // No available spot found\n    } catch (error) {\n        console.error('Find available spot error:', error);\n        return null;\n    }\n}\n// Process direct referral bonus (10% of investment) - Added directly to sponsor's wallet\n// ONLY active sponsors (with active mining units) receive commissions\n// ONLY paid ONCE per user (first purchase only)\nasync function processDirectReferralBonus(referrerId, investmentAmount, purchaserId) {\n    try {\n        // Check if sponsor is active (has active mining units)\n        const isActive = await hasActiveMiningUnits(referrerId);\n        if (!isActive) {\n            console.log(`Skipping direct referral bonus for inactive sponsor ${referrerId} - no active mining units`);\n            return 0; // Return 0 commission for inactive sponsors\n        }\n        // Check if this user has already received first commission from this purchaser\n        if (purchaserId) {\n            const purchaser = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: purchaserId\n                },\n                select: {\n                    hasReceivedFirstCommission: true,\n                    firstName: true,\n                    lastName: true\n                }\n            });\n            if (purchaser?.hasReceivedFirstCommission) {\n                console.log(`Skipping direct referral bonus - sponsor ${referrerId} already received first commission from user ${purchaserId} (${purchaser.firstName} ${purchaser.lastName})`);\n                return 0; // Return 0 commission for subsequent purchases\n            }\n        }\n        const bonusPercentage = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('DIRECT_REFERRAL_BONUS') || '10');\n        const bonusAmount = investmentAmount * bonusPercentage / 100;\n        // Create direct referral transaction first\n        const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n            userId: referrerId,\n            type: 'DIRECT_REFERRAL',\n            amount: bonusAmount,\n            description: `Direct referral bonus (${bonusPercentage}% of $${investmentAmount}) - First purchase`,\n            reference: purchaserId ? `from_user:${purchaserId}` : 'direct_referral',\n            status: 'COMPLETED'\n        });\n        // Allocate earnings to sponsor's mining units using FIFO logic\n        try {\n            const allocations = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(referrerId, bonusAmount, 'DIRECT_REFERRAL', transaction.id, `Direct referral commission from ${purchaserId ? 'user purchase' : 'referral'}`);\n            console.log(`Allocated ${bonusAmount} referral bonus to ${allocations.length} mining units for sponsor ${referrerId}`);\n            // Always add to wallet balance regardless of mining unit allocation\n            // This ensures the commission is available for withdrawal\n            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(referrerId, bonusAmount);\n            console.log(`Added ${bonusAmount} referral bonus to wallet balance for sponsor ${referrerId}`);\n        } catch (allocationError) {\n            console.error(`Failed to allocate referral bonus to mining units for ${referrerId}:`, allocationError);\n            // Fallback: Add to wallet balance if allocation fails\n            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(referrerId, bonusAmount);\n            console.log(`Fallback: Added ${bonusAmount} referral bonus directly to wallet for sponsor ${referrerId}`);\n        }\n        // Update referral commission earned\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.updateMany({\n            where: {\n                referrerId,\n                referred: {\n                    miningUnits: {\n                        some: {\n                            investmentAmount\n                        }\n                    }\n                }\n            },\n            data: {\n                commissionEarned: {\n                    increment: bonusAmount\n                }\n            }\n        });\n        // Mark the purchaser as having received their first commission\n        if (purchaserId) {\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n                where: {\n                    id: purchaserId\n                },\n                data: {\n                    hasReceivedFirstCommission: true\n                }\n            });\n        }\n        console.log(`First-time direct referral bonus of $${bonusAmount} awarded to active sponsor ${referrerId} from user ${purchaserId}`);\n        return bonusAmount;\n    } catch (error) {\n        console.error('Direct referral bonus error:', error);\n        throw error;\n    }\n}\n// Add points to binary system when someone makes an investment ($100 = 1 point)\nasync function addBinaryPoints(userId, investmentAmount) {\n    try {\n        // Calculate points: $100 investment = 1 point (with 2 decimal precision)\n        // $150 = 1.5 points, $250 = 2.5 points, etc.\n        const points = Math.round(investmentAmount / 100 * 100) / 100; // Round to 2 decimal places\n        if (points <= 0) return; // No points to add if investment is less than $100\n        // Find all upline users and add points to their binary system (ONLY active upliners)\n        const uplineUsers = await getUplineUsers(userId);\n        for (const uplineUser of uplineUsers){\n            // Check if upline user is active (has active mining units)\n            const isActive = await hasActiveMiningUnits(uplineUser.id);\n            if (!isActive) {\n                console.log(`Skipping inactive user ${uplineUser.id} - no active mining units`);\n                continue; // Skip inactive users\n            }\n            // Determine which side this user is on relative to upline\n            const placementSide = await getUserPlacementSide(uplineUser.id, userId);\n            if (placementSide) {\n                // Get current binary points for this user\n                const currentBinaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(uplineUser.id);\n                // Get max points per side setting\n                const maxPointsPerSide = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '10');\n                // Check current points on the target side\n                const currentLeftPoints = currentBinaryPoints?.leftPoints || 0;\n                const currentRightPoints = currentBinaryPoints?.rightPoints || 0;\n                let pointsToAdd = 0;\n                let sideToUpdate = placementSide;\n                if (placementSide === 'LEFT') {\n                    // Check if left side has reached the maximum\n                    if (currentLeftPoints >= maxPointsPerSide) {\n                        console.log(`User ${uplineUser.id} left side has reached maximum (${currentLeftPoints}/${maxPointsPerSide}). No points added.`);\n                        continue; // Skip adding points to this user\n                    }\n                    // Calculate how many points can be added without exceeding the limit\n                    pointsToAdd = Math.min(points, maxPointsPerSide - currentLeftPoints);\n                } else {\n                    // Check if right side has reached the maximum\n                    if (currentRightPoints >= maxPointsPerSide) {\n                        console.log(`User ${uplineUser.id} right side has reached maximum (${currentRightPoints}/${maxPointsPerSide}). No points added.`);\n                        continue; // Skip adding points to this user\n                    }\n                    // Calculate how many points can be added without exceeding the limit\n                    pointsToAdd = Math.min(points, maxPointsPerSide - currentRightPoints);\n                }\n                // Only add points if there's room\n                if (pointsToAdd > 0) {\n                    const updateData = placementSide === 'LEFT' ? {\n                        leftPoints: pointsToAdd\n                    } : {\n                        rightPoints: pointsToAdd\n                    };\n                    await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.upsert({\n                        userId: uplineUser.id,\n                        ...updateData\n                    });\n                    console.log(`Added ${pointsToAdd} points to ${placementSide} side for active user ${uplineUser.id} (${pointsToAdd < points ? 'capped at limit' : 'full amount'})`);\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Binary points addition error:', error);\n        throw error;\n    }\n}\n// Get all upline users for a given user\nasync function getUplineUsers(userId) {\n    try {\n        const uplineUsers = [];\n        let currentUserId = userId;\n        // Traverse up the tree (maximum 10 levels to prevent infinite loops)\n        for(let level = 0; level < 10; level++){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                },\n                include: {\n                    referrer: {\n                        select: {\n                            id: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            if (!referral) break;\n            uplineUsers.push(referral.referrer);\n            currentUserId = referral.referrerId;\n        }\n        return uplineUsers;\n    } catch (error) {\n        console.error('Upline users fetch error:', error);\n        return [];\n    }\n}\n// Get all ACTIVE upline users for a given user (skip inactive users)\nasync function getActiveUplineUsers(userId) {\n    try {\n        const uplineUsers = [];\n        let currentUserId = userId;\n        // Traverse up the tree (maximum 10 levels to prevent infinite loops)\n        for(let level = 0; level < 10; level++){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                },\n                include: {\n                    referrer: {\n                        select: {\n                            id: true,\n                            email: true,\n                            isActive: true\n                        }\n                    }\n                }\n            });\n            if (!referral) break;\n            // Only add active users to the list\n            if (referral.referrer.isActive) {\n                uplineUsers.push(referral.referrer);\n            }\n            // Continue traversing up regardless of active status\n            currentUserId = referral.referrerId;\n        }\n        return uplineUsers;\n    } catch (error) {\n        console.error('Active upline users fetch error:', error);\n        return [];\n    }\n}\n// Determine which side a user is on relative to an upline user\nasync function getUserPlacementSide(uplineUserId, userId) {\n    try {\n        // Check direct placement first\n        const directReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: uplineUserId,\n                referredId: userId\n            }\n        });\n        if (directReferral) {\n            return directReferral.placementSide;\n        }\n        // Check indirect placement by traversing down the tree\n        const leftSideUsers = await getDownlineUsers(uplineUserId, 'LEFT');\n        const rightSideUsers = await getDownlineUsers(uplineUserId, 'RIGHT');\n        if (leftSideUsers.some((u)=>u.id === userId)) {\n            return 'LEFT';\n        }\n        if (rightSideUsers.some((u)=>u.id === userId)) {\n            return 'RIGHT';\n        }\n        return null;\n    } catch (error) {\n        console.error('Placement side determination error:', error);\n        return null;\n    }\n}\n// Get all downline users for a specific side\nasync function getDownlineUsers(userId, side) {\n    try {\n        const downlineUsers = [];\n        const visited = new Set();\n        // Start with the direct placement on the specified side\n        const initialReferrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId: userId,\n                placementSide: side\n            },\n            select: {\n                referredId: true\n            }\n        });\n        // Use BFS to traverse the entire subtree\n        const queue = initialReferrals.map((r)=>r.referredId);\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            // Skip if already visited (prevent infinite loops)\n            if (visited.has(currentUserId)) continue;\n            visited.add(currentUserId);\n            // Add current user to downline\n            downlineUsers.push({\n                id: currentUserId\n            });\n            // Get all referrals (both LEFT and RIGHT) from current user\n            const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                where: {\n                    referrerId: currentUserId\n                },\n                select: {\n                    referredId: true\n                }\n            });\n            // Add all children to queue for further traversal\n            for (const referral of referrals){\n                if (!visited.has(referral.referredId)) {\n                    queue.push(referral.referredId);\n                }\n            }\n        }\n        return downlineUsers;\n    } catch (error) {\n        console.error('Downline users fetch error:', error);\n        return [];\n    }\n}\n// Get all downline users (both sides combined) for total team count\nasync function getAllDownlineUsers(userId) {\n    try {\n        const downlineUsers = [];\n        const visited = new Set();\n        // Get all direct referrals (both LEFT and RIGHT)\n        const initialReferrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId: userId\n            },\n            select: {\n                referredId: true\n            }\n        });\n        // Use BFS to traverse the entire binary tree\n        const queue = initialReferrals.map((r)=>r.referredId);\n        while(queue.length > 0){\n            const currentUserId = queue.shift();\n            // Skip if already visited (prevent infinite loops)\n            if (visited.has(currentUserId)) continue;\n            visited.add(currentUserId);\n            // Get user info including active status\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true,\n                    isActive: true\n                }\n            });\n            if (user) {\n                downlineUsers.push({\n                    id: user.id,\n                    isActive: user.isActive\n                });\n                // Get all referrals from current user\n                const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                    where: {\n                        referrerId: currentUserId\n                    },\n                    select: {\n                        referredId: true\n                    }\n                });\n                // Add all children to queue for further traversal\n                for (const referral of referrals){\n                    if (!visited.has(referral.referredId)) {\n                        queue.push(referral.referredId);\n                    }\n                }\n            }\n        }\n        return downlineUsers;\n    } catch (error) {\n        console.error('All downline users fetch error:', error);\n        return [];\n    }\n}\n// Process weekly binary matching (15:00 UTC on Saturdays)\nasync function processBinaryMatching() {\n    try {\n        console.log('Starting binary matching process...');\n        const maxPointsPerSide = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '10');\n        const pointValue = parseFloat(await _database__WEBPACK_IMPORTED_MODULE_1__.adminSettingsDb.get('BINARY_POINT_VALUE') || '10'); // Dynamic point value from settings\n        // Get all users with binary points\n        const usersWithPoints = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findMany({\n            where: {\n                OR: [\n                    {\n                        leftPoints: {\n                            gt: 0\n                        }\n                    },\n                    {\n                        rightPoints: {\n                            gt: 0\n                        }\n                    }\n                ]\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        console.log(`Processing binary matching for ${usersWithPoints.length} users`);\n        const matchingResults = [];\n        for (const userPoints of usersWithPoints){\n            try {\n                // Calculate matching points (minimum of left and right, capped at max per side)\n                const leftPoints = Math.min(userPoints.leftPoints, maxPointsPerSide);\n                const rightPoints = Math.min(userPoints.rightPoints, maxPointsPerSide);\n                const matchedPoints = Math.min(leftPoints, rightPoints);\n                if (matchedPoints > 0) {\n                    // Calculate direct payout: 1 point = $10\n                    const userPayout = matchedPoints * pointValue;\n                    try {\n                        // Create binary bonus transaction first\n                        const transaction = await _database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n                            userId: userPoints.userId,\n                            type: 'BINARY_BONUS',\n                            amount: userPayout,\n                            description: `Binary matching bonus - ${matchedPoints} points matched at $${pointValue} per point`,\n                            status: 'COMPLETED'\n                        });\n                        // Allocate earnings to user's mining units using FIFO logic\n                        try {\n                            const allocations = await (0,_miningUnitEarnings__WEBPACK_IMPORTED_MODULE_2__.allocateEarningsToUnits)(userPoints.userId, userPayout, 'BINARY_BONUS', transaction.id, `Binary matching bonus - ${matchedPoints} points matched`);\n                            console.log(`Allocated ${userPayout} binary bonus to ${allocations.length} mining units for user ${userPoints.userId}`);\n                            // Always add to wallet balance regardless of mining unit allocation\n                            // This ensures the bonus is available for withdrawal\n                            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(userPoints.userId, userPayout);\n                            console.log(`Added ${userPayout} binary bonus to wallet balance for user ${userPoints.userId}`);\n                        } catch (allocationError) {\n                            console.error(`Failed to allocate binary bonus to mining units for ${userPoints.userId}:`, allocationError);\n                            // Fallback: Add to wallet balance if allocation fails\n                            await _database__WEBPACK_IMPORTED_MODULE_1__.walletBalanceDb.addEarnings(userPoints.userId, userPayout);\n                            console.log(`Fallback: Added ${userPayout} binary bonus directly to wallet for user ${userPoints.userId}`);\n                        }\n                        // Calculate remaining points after matching - reset weaker side to 0\n                        // Example: User has 7 left, 5 right -> 5 matched, left becomes 2, right becomes 0\n                        const remainingLeftPoints = Math.max(0, userPoints.leftPoints - matchedPoints);\n                        const remainingRightPoints = Math.max(0, userPoints.rightPoints - matchedPoints);\n                        // Reset the weaker side to 0 after matching (proper binary matching rule)\n                        const finalLeftPoints = userPoints.leftPoints > userPoints.rightPoints ? remainingLeftPoints : 0;\n                        const finalRightPoints = userPoints.rightPoints > userPoints.leftPoints ? remainingRightPoints : 0;\n                        // Update binary points - reset weaker side to 0, keep stronger side remainder\n                        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n                            where: {\n                                id: userPoints.id\n                            },\n                            data: {\n                                leftPoints: finalLeftPoints,\n                                rightPoints: finalRightPoints,\n                                matchedPoints: {\n                                    increment: matchedPoints\n                                },\n                                totalMatched: {\n                                    increment: matchedPoints\n                                },\n                                lastMatchDate: new Date(),\n                                flushDate: new Date()\n                            }\n                        });\n                        matchingResults.push({\n                            userId: userPoints.userId,\n                            matchedPoints,\n                            payout: userPayout,\n                            remainingLeftPoints: finalLeftPoints,\n                            remainingRightPoints: finalRightPoints\n                        });\n                        console.log(`User ${userPoints.userId}: ${matchedPoints} points matched, $${userPayout.toFixed(2)} payout, remaining: L${finalLeftPoints} R${finalRightPoints}`);\n                    } catch (payoutError) {\n                        console.error(`Error processing payout for user ${userPoints.userId}:`, payoutError);\n                    // Continue with next user instead of failing the entire process\n                    }\n                } else {\n                    // No matching possible, but still reset excess points if over the limit\n                    const excessLeft = Math.max(0, userPoints.leftPoints - maxPointsPerSide);\n                    const excessRight = Math.max(0, userPoints.rightPoints - maxPointsPerSide);\n                    if (excessLeft > 0 || excessRight > 0) {\n                        try {\n                            // Reset excess points (pressure out)\n                            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n                                where: {\n                                    id: userPoints.id\n                                },\n                                data: {\n                                    leftPoints: Math.min(userPoints.leftPoints, maxPointsPerSide),\n                                    rightPoints: Math.min(userPoints.rightPoints, maxPointsPerSide),\n                                    flushDate: new Date()\n                                }\n                            });\n                            console.log(`User ${userPoints.userId}: Excess points reset - L${excessLeft} R${excessRight} points flushed`);\n                        } catch (flushError) {\n                            console.error(`Error flushing excess points for user ${userPoints.userId}:`, flushError);\n                        }\n                    }\n                }\n            } catch (userError) {\n                console.error(`Error processing binary matching for user ${userPoints.userId}:`, userError);\n            }\n        }\n        // Log the binary matching process\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: 'BINARY_MATCHING_PROCESSED',\n            details: {\n                usersProcessed: usersWithPoints.length,\n                totalMatchedPoints: matchingResults.reduce((sum, r)=>sum + r.matchedPoints, 0),\n                pointValue,\n                totalPayouts: matchingResults.reduce((sum, r)=>sum + r.payout, 0),\n                timestamp: new Date().toISOString()\n            }\n        });\n        console.log(`Binary matching completed. Processed ${matchingResults.length} users with total payouts: $${matchingResults.reduce((sum, r)=>sum + r.payout, 0).toFixed(2)}`);\n        return {\n            success: true,\n            usersProcessed: matchingResults.length,\n            totalPayouts: matchingResults.reduce((sum, r)=>sum + r.payout, 0),\n            matchingResults\n        };\n    } catch (error) {\n        console.error('Binary matching process error:', error);\n        throw error;\n    }\n}\n// Get sponsor information for a user\nasync function getSponsorInfo(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                referrerId: true\n            }\n        });\n        if (!user?.referrerId) return null;\n        const sponsor = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: user.referrerId\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true\n            }\n        });\n        return sponsor;\n    } catch (error) {\n        console.error('Sponsor info fetch error:', error);\n        return null;\n    }\n}\n// Get direct referral count for a user (sponsored users)\nasync function getDirectReferralCount(userId) {\n    try {\n        const count = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count({\n            where: {\n                referrerId: userId\n            }\n        });\n        return count;\n    } catch (error) {\n        console.error('Direct referral count error:', error);\n        return 0;\n    }\n}\n// Get total team count (all downline users in binary tree) - uses cached values\nasync function getTotalTeamCount(userId) {\n    try {\n        return await getCachedDownlineCounts(userId);\n    } catch (error) {\n        console.error('Total team count error:', error);\n        return {\n            left: 0,\n            right: 0,\n            total: 0\n        };\n    }\n}\n// Get detailed team statistics\nasync function getDetailedTeamStats(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                directReferralCount: true\n            }\n        });\n        const teamCounts = await getCachedDownlineCounts(userId);\n        // Get all downline users for accurate active member count\n        const allDownlineUsers = await getAllDownlineUsers(userId);\n        const activeMembers = allDownlineUsers.filter((u)=>u.isActive).length;\n        // Get recent joins (last 30 days) - direct referrals only\n        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n        const recentJoins = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.count({\n            where: {\n                referrerId: userId,\n                createdAt: {\n                    gte: thirtyDaysAgo\n                }\n            }\n        });\n        return {\n            directReferrals: user?.directReferralCount || 0,\n            leftTeam: teamCounts.left,\n            rightTeam: teamCounts.right,\n            totalTeam: teamCounts.total,\n            activeMembers,\n            recentJoins\n        };\n    } catch (error) {\n        console.error('Detailed team stats error:', error);\n        return {\n            directReferrals: 0,\n            leftTeam: 0,\n            rightTeam: 0,\n            totalTeam: 0,\n            activeMembers: 0,\n            recentJoins: 0\n        };\n    }\n}\n// Find all users in a specific generation (level) of the tree\nasync function getUsersByGeneration(userId, generation) {\n    try {\n        if (generation <= 0) return [];\n        let currentLevelUsers = [\n            {\n                id: userId,\n                side: null\n            }\n        ];\n        for(let level = 1; level <= generation; level++){\n            const nextLevelUsers = [];\n            for (const currentUser of currentLevelUsers){\n                const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                    where: {\n                        referrerId: currentUser.id\n                    },\n                    include: {\n                        referred: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true,\n                                createdAt: true\n                            }\n                        }\n                    }\n                });\n                for (const referral of referrals){\n                    nextLevelUsers.push({\n                        id: referral.referredId,\n                        side: referral.placementSide\n                    });\n                }\n            }\n            currentLevelUsers = nextLevelUsers;\n        }\n        // Get full user details for the final generation\n        const userDetails = await Promise.all(currentLevelUsers.map(async (user)=>{\n            const userInfo = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: user.id\n                },\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true,\n                    createdAt: true\n                }\n            });\n            return {\n                ...userInfo,\n                placementSide: user.side\n            };\n        }));\n        return userDetails.filter(Boolean);\n    } catch (error) {\n        console.error('Users by generation error:', error);\n        return [];\n    }\n}\n// Enhanced binary tree structure with detailed member information\nasync function getBinaryTreeStructure(userId, depth = 3, expandedNodes = new Set()) {\n    try {\n        const buildTree = async (currentUserId, currentDepth, path = '')=>{\n            if (currentDepth <= 0) return null;\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: currentUserId\n                },\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true,\n                    createdAt: true\n                }\n            });\n            if (!user) return null;\n            // Check if user has active mining units (for binary tree display)\n            const isActive = await hasActiveMiningUnits(currentUserId);\n            // Get sponsor information\n            const sponsorInfo = await getSponsorInfo(currentUserId);\n            // Get direct referral count\n            const directReferralCount = await getDirectReferralCount(currentUserId);\n            // Get team counts\n            const teamCounts = await getTotalTeamCount(currentUserId);\n            // Get direct referrals (binary placement)\n            const leftReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: currentUserId,\n                    placementSide: 'LEFT'\n                },\n                include: {\n                    referred: {\n                        select: {\n                            id: true,\n                            email: true,\n                            firstName: true,\n                            lastName: true,\n                            createdAt: true\n                        }\n                    }\n                }\n            });\n            const rightReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: currentUserId,\n                    placementSide: 'RIGHT'\n                },\n                include: {\n                    referred: {\n                        select: {\n                            id: true,\n                            email: true,\n                            firstName: true,\n                            lastName: true,\n                            createdAt: true\n                        }\n                    }\n                }\n            });\n            // Get binary points\n            const binaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(currentUserId);\n            // Determine if we should load children for infinite depth support\n            // Load children if we have remaining depth AND either:\n            // 1. We're within the initial depth (first 3 levels) - always show first 3 levels\n            // 2. OR this node is explicitly expanded - show children of expanded nodes\n            const isWithinInitialDepth = path.length < 3; // First 3 levels (root = 0, level 1 = 1 char, level 2 = 2 chars)\n            const isNodeExpanded = expandedNodes.has(currentUserId);\n            const shouldLoadChildren = currentDepth > 1 && (isWithinInitialDepth || isNodeExpanded);\n            // Check if children exist (for showing expand button)\n            const hasLeftChild = leftReferral !== null;\n            const hasRightChild = rightReferral !== null;\n            return {\n                user: {\n                    ...user,\n                    isActive\n                },\n                sponsorInfo,\n                directReferralCount,\n                teamCounts,\n                binaryPoints: binaryPoints || {\n                    leftPoints: 0,\n                    rightPoints: 0,\n                    matchedPoints: 0\n                },\n                hasLeftChild,\n                hasRightChild,\n                leftChild: shouldLoadChildren && leftReferral ? await buildTree(leftReferral.referredId, currentDepth - 1, path + 'L') : null,\n                rightChild: shouldLoadChildren && rightReferral ? await buildTree(rightReferral.referredId, currentDepth - 1, path + 'R') : null\n            };\n        };\n        return await buildTree(userId, depth);\n    } catch (error) {\n        console.error('Binary tree structure error:', error);\n        throw error;\n    }\n}\n// Load children for a specific node (for dynamic expansion)\nasync function loadNodeChildren(userId) {\n    try {\n        // Get direct referrals (binary placement)\n        const leftReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: userId,\n                placementSide: 'LEFT'\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n        const rightReferral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n            where: {\n                referrerId: userId,\n                placementSide: 'RIGHT'\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n        const buildChildNode = async (referral)=>{\n            if (!referral) return null;\n            const childUserId = referral.referredId;\n            // Check if user has active mining units (for binary tree display)\n            const isActive = await hasActiveMiningUnits(childUserId);\n            // Get sponsor information\n            const sponsorInfo = await getSponsorInfo(childUserId);\n            // Get direct referral count\n            const directReferralCount = await getDirectReferralCount(childUserId);\n            // Get team counts\n            const teamCounts = await getTotalTeamCount(childUserId);\n            // Get binary points\n            const binaryPoints = await _database__WEBPACK_IMPORTED_MODULE_1__.binaryPointsDb.findByUserId(childUserId);\n            // Check if this child has its own children\n            const hasLeftChild = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: childUserId,\n                    placementSide: 'LEFT'\n                },\n                select: {\n                    id: true\n                }\n            }) !== null;\n            const hasRightChild = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referrerId: childUserId,\n                    placementSide: 'RIGHT'\n                },\n                select: {\n                    id: true\n                }\n            }) !== null;\n            return {\n                user: {\n                    ...referral.referred,\n                    isActive\n                },\n                sponsorInfo,\n                directReferralCount,\n                teamCounts,\n                binaryPoints: binaryPoints || {\n                    leftPoints: 0,\n                    rightPoints: 0,\n                    matchedPoints: 0\n                },\n                hasLeftChild,\n                hasRightChild,\n                leftChild: null,\n                rightChild: null\n            };\n        };\n        const leftChild = await buildChildNode(leftReferral);\n        const rightChild = await buildChildNode(rightReferral);\n        return {\n            leftChild,\n            rightChild\n        };\n    } catch (error) {\n        console.error('Load node children error:', error);\n        return {\n            leftChild: null,\n            rightChild: null\n        };\n    }\n}\n// Search for users in the binary tree\nasync function searchUsersInTree(rootUserId, searchTerm, maxResults = 20) {\n    try {\n        const searchPattern = `%${searchTerm.toLowerCase()}%`;\n        // Get all downline users that match the search term\n        const leftUsers = await getDownlineUsers(rootUserId, 'LEFT');\n        const rightUsers = await getDownlineUsers(rootUserId, 'RIGHT');\n        const allDownlineIds = [\n            ...leftUsers,\n            ...rightUsers\n        ].map((u)=>u.id);\n        if (allDownlineIds.length === 0) return [];\n        const matchingUsers = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n            where: {\n                id: {\n                    in: allDownlineIds\n                },\n                OR: [\n                    {\n                        email: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    },\n                    {\n                        firstName: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    },\n                    {\n                        lastName: {\n                            contains: searchTerm,\n                            mode: 'insensitive'\n                        }\n                    }\n                ]\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                createdAt: true,\n                referrerId: true\n            },\n            take: maxResults\n        });\n        // Get placement path and sponsor info for each user\n        const results = await Promise.all(matchingUsers.map(async (user)=>{\n            const placementPath = await getPlacementPath(rootUserId, user.id);\n            const generation = placementPath.split('-').length;\n            let sponsorInfo = undefined;\n            if (user.referrerId) {\n                sponsorInfo = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                    where: {\n                        id: user.referrerId\n                    },\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                });\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                firstName: user.firstName,\n                lastName: user.lastName,\n                createdAt: user.createdAt,\n                placementPath,\n                generation,\n                sponsorInfo: sponsorInfo || undefined\n            };\n        }));\n        return results;\n    } catch (error) {\n        console.error('Search users in tree error:', error);\n        return [];\n    }\n}\n// Get placement path from root to a specific user (e.g., \"L-R-L\")\nasync function getPlacementPath(rootUserId, targetUserId) {\n    try {\n        if (rootUserId === targetUserId) return 'ROOT';\n        const path = [];\n        let currentUserId = targetUserId;\n        // Traverse up the tree to find path\n        while(currentUserId !== rootUserId){\n            const referral = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findFirst({\n                where: {\n                    referredId: currentUserId\n                }\n            });\n            if (!referral) break;\n            path.unshift(referral.placementSide === 'LEFT' ? 'L' : 'R');\n            currentUserId = referral.referrerId;\n            // Prevent infinite loops\n            if (path.length > 20) break;\n        }\n        return path.join('-') || 'UNKNOWN';\n    } catch (error) {\n        console.error('Get placement path error:', error);\n        return 'UNKNOWN';\n    }\n}\n// Update tree counts after a new user placement\nasync function updateTreeCountsAfterPlacement(sponsorId, placementParentId) {\n    try {\n        // Update counts for the sponsor (if different from placement parent)\n        if (sponsorId !== placementParentId) {\n            await updateCachedDownlineCounts(sponsorId);\n        }\n        // Update counts for the placement parent\n        await updateCachedDownlineCounts(placementParentId);\n        // Update counts for all upline users from the placement parent\n        const uplineUsers = await getUplineUsers(placementParentId);\n        const updatePromises = uplineUsers.map((user)=>updateCachedDownlineCounts(user.id));\n        await Promise.all(updatePromises);\n    } catch (error) {\n        console.error('Update tree counts after placement error:', error);\n    // Don't throw error as this is supplementary to placement\n    }\n}\n// Bulk update tree counts for multiple users (for maintenance)\nasync function bulkUpdateTreeCounts(userIds) {\n    try {\n        const updatePromises = userIds.map((userId)=>updateCachedDownlineCounts(userId));\n        await Promise.all(updatePromises);\n    } catch (error) {\n        console.error('Bulk update tree counts error:', error);\n    }\n}\n// Get tree health statistics\nasync function getTreeHealthStats(rootUserId) {\n    try {\n        const teamCounts = await getCachedDownlineCounts(rootUserId);\n        const totalUsers = teamCounts.total;\n        // Calculate balance ratio\n        const smallerSide = Math.min(teamCounts.left, teamCounts.right);\n        const largerSide = Math.max(teamCounts.left, teamCounts.right);\n        const balanceRatio = largerSide > 0 ? smallerSide / largerSide : 1;\n        // Calculate tree depth statistics\n        let maxDepth = 0;\n        let totalDepth = 0;\n        let userCount = 0;\n        // BFS to calculate depths\n        const queue = [\n            {\n                userId: rootUserId,\n                depth: 0\n            }\n        ];\n        const visited = new Set();\n        while(queue.length > 0){\n            const { userId, depth } = queue.shift();\n            if (visited.has(userId)) continue;\n            visited.add(userId);\n            maxDepth = Math.max(maxDepth, depth);\n            totalDepth += depth;\n            userCount++;\n            const referrals = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n                where: {\n                    referrerId: userId\n                },\n                select: {\n                    referredId: true\n                }\n            });\n            for (const referral of referrals){\n                if (!visited.has(referral.referredId)) {\n                    queue.push({\n                        userId: referral.referredId,\n                        depth: depth + 1\n                    });\n                }\n            }\n        }\n        const averageDepth = userCount > 0 ? totalDepth / userCount : 0;\n        // Calculate empty positions (theoretical max - actual users)\n        const theoreticalMax = Math.pow(2, maxDepth + 1) - 1;\n        const emptyPositions = Math.max(0, theoreticalMax - totalUsers);\n        return {\n            totalUsers,\n            balanceRatio,\n            averageDepth,\n            maxDepth,\n            emptyPositions\n        };\n    } catch (error) {\n        console.error('Tree health stats error:', error);\n        return {\n            totalUsers: 0,\n            balanceRatio: 1,\n            averageDepth: 0,\n            maxDepth: 0,\n            emptyPositions: 0\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/referral.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmining-units%2Froute&page=%2Fapi%2Fmining-units%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmining-units%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();