"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_emailNotificationService_ts";
exports.ids = ["_rsc_src_lib_emailNotificationService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/email.ts":
/*!**************************!*\
  !*** ./src/lib/email.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emailService: () => (/* binding */ emailService),\n/* harmony export */   generateOTP: () => (/* binding */ generateOTP),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\nclass EmailService {\n    async getEmailConfig() {\n        try {\n            const settings = await _database__WEBPACK_IMPORTED_MODULE_1__.systemSettingsDb.getEmailSettings();\n            if (!settings || !settings.smtpHost || !settings.smtpUser || !settings.smtpPassword) {\n                console.warn('Email configuration not found or incomplete');\n                return null;\n            }\n            return {\n                host: settings.smtpHost,\n                port: settings.smtpPort || 587,\n                secure: settings.smtpSecure || false,\n                user: settings.smtpUser,\n                password: settings.smtpPassword,\n                fromName: settings.fromName || 'HashCoreX',\n                fromEmail: settings.fromEmail || settings.smtpUser\n            };\n        } catch (error) {\n            console.error('Failed to get email configuration:', error);\n            return null;\n        }\n    }\n    async initializeTransporter() {\n        try {\n            this.config = await this.getEmailConfig();\n            if (!this.config) {\n                return false;\n            }\n            this.transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n                host: this.config.host,\n                port: this.config.port,\n                secure: this.config.secure,\n                auth: {\n                    user: this.config.user,\n                    pass: this.config.password\n                },\n                tls: {\n                    rejectUnauthorized: false\n                }\n            });\n            // Verify connection\n            await this.transporter.verify();\n            console.log('Email transporter initialized successfully');\n            return true;\n        } catch (error) {\n            console.error('Failed to initialize email transporter:', error);\n            this.transporter = null;\n            return false;\n        }\n    }\n    async sendEmail(emailData) {\n        try {\n            if (!this.transporter || !this.config) {\n                const initialized = await this.initializeTransporter();\n                if (!initialized) {\n                    throw new Error('Email service not configured');\n                }\n            }\n            const mailOptions = {\n                from: `\"${this.config.fromName}\" <${this.config.fromEmail}>`,\n                to: emailData.to,\n                subject: emailData.subject,\n                html: emailData.html,\n                text: emailData.text\n            };\n            const result = await this.transporter.sendMail(mailOptions);\n            console.log('Email sent successfully:', result.messageId);\n            return true;\n        } catch (error) {\n            console.error('Failed to send email:', error);\n            return false;\n        }\n    }\n    async sendOTPEmail(email, otp, firstName, purpose = 'email_verification') {\n        const templateName = purpose === 'password_reset' ? 'password_reset_otp' : 'otp_verification';\n        const template = await this.getEmailTemplate(templateName);\n        if (!template) {\n            // Fallback template based on purpose\n            const isPasswordReset = purpose === 'password_reset';\n            const subject = isPasswordReset ? 'Password Reset - HashCoreX' : 'Verify Your Email - HashCoreX';\n            const title = isPasswordReset ? 'Password Reset' : 'Email Verification';\n            const message = isPasswordReset ? 'You requested to reset your password. Please use the following OTP to proceed:' : 'Thank you for registering with HashCoreX. Please use the following OTP to verify your email address:';\n            const html = `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <div style=\"background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;\">\n            <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n          </div>\n          <div style=\"padding: 30px; background: #f9f9f9;\">\n            <h2 style=\"color: #333;\">${title}</h2>\n            <p>Hello ${firstName || 'User'},</p>\n            <p>${message}</p>\n            <div style=\"background: white; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; border: 2px solid #ffd60a;\">\n              <h1 style=\"color: #ffd60a; font-size: 32px; margin: 0; letter-spacing: 5px;\">${otp}</h1>\n            </div>\n            <p>This OTP will expire in 10 minutes for security reasons.</p>\n            <p>If you didn't request this ${isPasswordReset ? 'password reset' : 'verification'}, please ignore this email.</p>\n            <p>Best regards,<br>The HashCoreX Team</p>\n          </div>\n          <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n            <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n          </div>\n        </div>\n      `;\n            return await this.sendEmail({\n                to: email,\n                subject,\n                html,\n                text: `HashCoreX ${title}\\n\\nYour OTP: ${otp}\\n\\nThis OTP will expire in 10 minutes.`\n            });\n        }\n        // Use custom template\n        let html = template.htmlContent;\n        let text = template.textContent || '';\n        // Replace placeholders\n        html = html.replace(/{{firstName}}/g, firstName || 'User');\n        html = html.replace(/{{otp}}/g, otp);\n        text = text.replace(/{{firstName}}/g, firstName || 'User');\n        text = text.replace(/{{otp}}/g, otp);\n        return await this.sendEmail({\n            to: email,\n            subject: template.subject,\n            html,\n            text\n        });\n    }\n    async getEmailTemplate(templateName) {\n        try {\n            const template = await _database__WEBPACK_IMPORTED_MODULE_1__.systemSettingsDb.getEmailTemplate(templateName);\n            return template;\n        } catch (error) {\n            console.error('Failed to get email template:', error);\n            return null;\n        }\n    }\n    async testConnection() {\n        try {\n            // Get fresh config\n            this.config = await this.getEmailConfig();\n            if (!this.config) {\n                console.error('Email configuration not found or incomplete');\n                return false;\n            }\n            // Create transporter\n            this.transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n                host: this.config.host,\n                port: this.config.port,\n                secure: this.config.secure,\n                auth: {\n                    user: this.config.user,\n                    pass: this.config.password\n                },\n                tls: {\n                    rejectUnauthorized: false\n                }\n            });\n            // Test connection\n            await this.transporter.verify();\n            console.log('Email connection test successful');\n            return true;\n        } catch (error) {\n            console.error('Email connection test failed:', error);\n            this.transporter = null;\n            throw error; // Re-throw to get specific error message\n        }\n    }\n    constructor(){\n        this.transporter = null;\n        this.config = null;\n    }\n}\n// Export singleton instance\nconst emailService = new EmailService();\n// Utility functions\nconst generateOTP = ()=>{\n    return Math.floor(100000 + Math.random() * 900000).toString();\n};\nconst isValidEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/emailNotificationService.ts":
/*!*********************************************!*\
  !*** ./src/lib/emailNotificationService.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emailNotificationService: () => (/* binding */ emailNotificationService)\n/* harmony export */ });\n/* harmony import */ var _email__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./email */ \"(rsc)/./src/lib/email.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\nclass EmailNotificationService {\n    /**\n   * Send deposit success notification\n   */ async sendDepositSuccessNotification(data) {\n        try {\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate('deposit_success');\n            const subject = template?.subject || 'Deposit Confirmed - HashCoreX';\n            let html = template?.htmlContent || this.getDefaultDepositSuccessTemplate();\n            let text = template?.textContent || '';\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                amount: data.amount.toString(),\n                transactionId: data.transactionId,\n                currency: data.currency\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                amount: data.amount.toString(),\n                transactionId: data.transactionId,\n                currency: data.currency\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: 'deposit_success',\n                status: 'PENDING'\n            });\n            const emailSent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text: text || `Deposit Confirmed\\n\\nAmount: ${data.amount} ${data.currency}\\nTransaction ID: ${data.transactionId}`\n            });\n            if (emailSent) {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'SENT');\n                return true;\n            } else {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');\n                return false;\n            }\n        } catch (error) {\n            console.error('Error sending deposit success notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Send KYC status notification\n   */ async sendKYCStatusNotification(data) {\n        try {\n            const templateName = data.status === 'APPROVED' ? 'kyc_approved' : 'kyc_rejected';\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate(templateName);\n            const subject = template?.subject || `KYC ${data.status === 'APPROVED' ? 'Approved' : 'Rejected'} - HashCoreX`;\n            let html = template?.htmlContent || this.getDefaultKYCTemplate(data.status);\n            let text = template?.textContent || '';\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                status: data.status,\n                rejectionReason: data.rejectionReason || ''\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                status: data.status,\n                rejectionReason: data.rejectionReason || ''\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: templateName,\n                status: 'PENDING'\n            });\n            const emailSent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text: text || `KYC ${data.status}\\n\\n${data.rejectionReason ? `Reason: ${data.rejectionReason}` : 'Your KYC has been approved.'}`\n            });\n            if (emailSent) {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'SENT');\n                return true;\n            } else {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');\n                return false;\n            }\n        } catch (error) {\n            console.error('Error sending KYC status notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Send withdrawal status notification\n   */ async sendWithdrawalStatusNotification(data) {\n        try {\n            const templateName = this.getWithdrawalTemplateName(data.status);\n            const template = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.getEmailTemplate(templateName);\n            const subject = template?.subject || `Withdrawal ${this.getWithdrawalStatusText(data.status)} - HashCoreX`;\n            let html = template?.htmlContent || this.getDefaultWithdrawalTemplate(data.status);\n            let text = template?.textContent || '';\n            // Replace variables\n            html = this.replaceVariables(html, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                amount: data.amount.toString(),\n                status: data.status,\n                transactionHash: data.transactionHash || '',\n                rejectionReason: data.rejectionReason || '',\n                usdtAddress: data.usdtAddress || ''\n            });\n            text = this.replaceVariables(text, {\n                firstName: data.firstName,\n                lastName: data.lastName,\n                email: data.email,\n                amount: data.amount.toString(),\n                status: data.status,\n                transactionHash: data.transactionHash || '',\n                rejectionReason: data.rejectionReason || '',\n                usdtAddress: data.usdtAddress || ''\n            });\n            // Create email log entry\n            const emailLog = await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n                to: data.email,\n                subject,\n                template: templateName,\n                status: 'PENDING'\n            });\n            const emailSent = await _email__WEBPACK_IMPORTED_MODULE_0__.emailService.sendEmail({\n                to: data.email,\n                subject,\n                html,\n                text: text || this.getDefaultWithdrawalText(data)\n            });\n            if (emailSent) {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'SENT');\n                return true;\n            } else {\n                await _database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');\n                return false;\n            }\n        } catch (error) {\n            console.error('Error sending withdrawal status notification:', error);\n            return false;\n        }\n    }\n    /**\n   * Replace template variables\n   */ replaceVariables(template, variables) {\n        let result = template;\n        for (const [key, value] of Object.entries(variables)){\n            const regex = new RegExp(`{{${key}}}`, 'g');\n            result = result.replace(regex, value);\n        }\n        return result;\n    }\n    /**\n   * Get withdrawal template name based on status\n   */ getWithdrawalTemplateName(status) {\n        switch(status){\n            case 'APPROVED':\n                return 'withdrawal_approved';\n            case 'REJECTED':\n                return 'withdrawal_rejected';\n            case 'COMPLETED':\n                return 'withdrawal_completed';\n            case 'FAILED':\n                return 'withdrawal_failed';\n            default:\n                return 'withdrawal_status';\n        }\n    }\n    /**\n   * Get withdrawal status text\n   */ getWithdrawalStatusText(status) {\n        switch(status){\n            case 'APPROVED':\n                return 'Approved';\n            case 'REJECTED':\n                return 'Rejected';\n            case 'COMPLETED':\n                return 'Completed';\n            case 'FAILED':\n                return 'Failed';\n            default:\n                return 'Updated';\n        }\n    }\n    /**\n   * Default deposit success template\n   */ getDefaultDepositSuccessTemplate() {\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f9f9f9;\">\n          <h2 style=\"color: #333;\">Deposit Confirmed!</h2>\n          <p>Hello {{firstName}},</p>\n          <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>\n          <div style=\"background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;\">\n            <h3 style=\"margin: 0 0 10px 0; color: #10b981;\">Deposit Details</h3>\n            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>\n            <p><strong>Transaction ID:</strong> {{transactionId}}</p>\n            <p><strong>Status:</strong> Confirmed</p>\n          </div>\n          <p>Your funds are now available in your wallet and you can start using them immediately.</p>\n          <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n        <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n          <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default KYC template\n   */ getDefaultKYCTemplate(status) {\n        const isApproved = status === 'APPROVED';\n        const color = isApproved ? '#10b981' : '#ef4444';\n        const title = isApproved ? 'KYC Approved!' : 'KYC Rejected';\n        const message = isApproved ? 'Congratulations! Your KYC verification has been approved. You now have full access to all platform features.' : 'Unfortunately, your KYC verification has been rejected. Please review the reason below and resubmit with correct documents.';\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #ffd60a 0%, ${color} 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f9f9f9;\">\n          <h2 style=\"color: #333;\">${title}</h2>\n          <p>Hello {{firstName}},</p>\n          <p>${message}</p>\n          ${!isApproved ? '<div style=\"background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;\"><p><strong>Rejection Reason:</strong> {{rejectionReason}}</p></div>' : ''}\n          <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n        <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n          <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default withdrawal template\n   */ getDefaultWithdrawalTemplate(status) {\n        const getStatusColor = (status)=>{\n            switch(status){\n                case 'APPROVED':\n                    return '#10b981';\n                case 'COMPLETED':\n                    return '#10b981';\n                case 'REJECTED':\n                    return '#ef4444';\n                case 'FAILED':\n                    return '#ef4444';\n                default:\n                    return '#6b7280';\n            }\n        };\n        return `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #ffd60a 0%, ${getStatusColor(status)} 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">HashCoreX</h1>\n        </div>\n        <div style=\"padding: 30px; background: #f9f9f9;\">\n          <h2 style=\"color: #333;\">Withdrawal {{status}}</h2>\n          <p>Hello {{firstName}},</p>\n          <p>Your withdrawal request has been {{status}}.</p>\n          <div style=\"background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid ${getStatusColor(status)};\">\n            <h3 style=\"margin: 0 0 10px 0; color: ${getStatusColor(status)};\">Withdrawal Details</h3>\n            <p><strong>Amount:</strong> {{amount}} USDT</p>\n            <p><strong>Status:</strong> {{status}}</p>\n            {{#if usdtAddress}}<p><strong>Address:</strong> {{usdtAddress}}</p>{{/if}}\n            {{#if transactionHash}}<p><strong>Transaction Hash:</strong> {{transactionHash}}</p>{{/if}}\n            {{#if rejectionReason}}<p><strong>Reason:</strong> {{rejectionReason}}</p>{{/if}}\n          </div>\n          <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n        <div style=\"background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;\">\n          <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n        </div>\n      </div>\n    `;\n    }\n    /**\n   * Default withdrawal text\n   */ getDefaultWithdrawalText(data) {\n        let text = `Withdrawal ${data.status}\\n\\nAmount: ${data.amount} USDT\\nStatus: ${data.status}`;\n        if (data.usdtAddress) text += `\\nAddress: ${data.usdtAddress}`;\n        if (data.transactionHash) text += `\\nTransaction Hash: ${data.transactionHash}`;\n        if (data.rejectionReason) text += `\\nReason: ${data.rejectionReason}`;\n        return text;\n    }\n}\nconst emailNotificationService = new EmailNotificationService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/emailNotificationService.ts\n");

/***/ })

};
;