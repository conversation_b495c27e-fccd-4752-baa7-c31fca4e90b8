import nodemailer from 'nodemailer';
import { systemSettingsDb } from './database';

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
  fromName: string;
  fromEmail: string;
}

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent?: string;
}

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private config: EmailConfig | null = null;

  async getEmailConfig(): Promise<EmailConfig | null> {
    try {
      const settings = await systemSettingsDb.getEmailSettings();
      if (!settings || !settings.smtpHost || !settings.smtpUser || !settings.smtpPassword) {
        console.warn('Email configuration not found or incomplete');
        return null;
      }

      return {
        host: settings.smtpHost,
        port: settings.smtpPort || 587,
        secure: settings.smtpSecure || false,
        user: settings.smtpUser,
        password: settings.smtpPassword,
        fromName: settings.fromName || 'HashCoreX',
        fromEmail: settings.fromEmail || settings.smtpUser,
      };
    } catch (error) {
      console.error('Failed to get email configuration:', error);
      return null;
    }
  }

  async initializeTransporter(): Promise<boolean> {
    try {
      this.config = await this.getEmailConfig();
      if (!this.config) {
        return false;
      }

      this.transporter = nodemailer.createTransporter({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        auth: {
          user: this.config.user,
          pass: this.config.password,
        },
        tls: {
          rejectUnauthorized: false, // For development/testing
        },
      });

      // Verify connection
      await this.transporter.verify();
      console.log('Email transporter initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
      this.transporter = null;
      return false;
    }
  }

  async sendEmail(emailData: EmailData): Promise<boolean> {
    try {
      if (!this.transporter || !this.config) {
        const initialized = await this.initializeTransporter();
        if (!initialized) {
          throw new Error('Email service not configured');
        }
      }

      const mailOptions = {
        from: `"${this.config!.fromName}" <${this.config!.fromEmail}>`,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      };

      const result = await this.transporter!.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  async sendOTPEmail(email: string, otp: string, firstName?: string): Promise<boolean> {
    const template = await this.getEmailTemplate('otp_verification');
    
    if (!template) {
      // Fallback template
      const subject = 'Verify Your Email - HashCoreX';
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">HashCoreX</h1>
          </div>
          <div style="padding: 30px; background: #f9f9f9;">
            <h2 style="color: #333;">Email Verification</h2>
            <p>Hello ${firstName || 'User'},</p>
            <p>Thank you for registering with HashCoreX. Please use the following OTP to verify your email address:</p>
            <div style="background: white; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; border: 2px solid #ffd60a;">
              <h1 style="color: #ffd60a; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
            </div>
            <p>This OTP will expire in 10 minutes for security reasons.</p>
            <p>If you didn't request this verification, please ignore this email.</p>
            <p>Best regards,<br>The HashCoreX Team</p>
          </div>
          <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
            <p>&copy; 2024 HashCoreX. All rights reserved.</p>
          </div>
        </div>
      `;
      
      return await this.sendEmail({
        to: email,
        subject,
        html,
        text: `HashCoreX Email Verification\n\nYour OTP: ${otp}\n\nThis OTP will expire in 10 minutes.`,
      });
    }

    // Use custom template
    let html = template.htmlContent;
    let text = template.textContent || '';
    
    // Replace placeholders
    html = html.replace(/{{firstName}}/g, firstName || 'User');
    html = html.replace(/{{otp}}/g, otp);
    text = text.replace(/{{firstName}}/g, firstName || 'User');
    text = text.replace(/{{otp}}/g, otp);

    return await this.sendEmail({
      to: email,
      subject: template.subject,
      html,
      text,
    });
  }

  async getEmailTemplate(templateName: string): Promise<EmailTemplate | null> {
    try {
      const template = await systemSettingsDb.getEmailTemplate(templateName);
      return template;
    } catch (error) {
      console.error('Failed to get email template:', error);
      return null;
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      // Get fresh config
      this.config = await this.getEmailConfig();
      if (!this.config) {
        console.error('Email configuration not found or incomplete');
        return false;
      }

      // Create transporter
      this.transporter = nodemailer.createTransporter({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        auth: {
          user: this.config.user,
          pass: this.config.password,
        },
        tls: {
          rejectUnauthorized: false,
        },
      });

      // Test connection
      await this.transporter.verify();
      console.log('Email connection test successful');
      return true;
    } catch (error) {
      console.error('Email connection test failed:', error);
      this.transporter = null;
      throw error; // Re-throw to get specific error message
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Utility functions
export const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
